{"name": "alcfront", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "lint": "eslint ."}, "dependencies": {"expo": "~50.0.14", "react": "18.2.0", "react-native": "0.73.6", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "@reduxjs/toolkit": "^2.0.1", "react-redux": "^9.0.4", "redux-persist": "^6.0.0", "@react-native-async-storage/async-storage": "1.21.0", "react-native-screens": "~3.29.0", "react-native-safe-area-context": "4.8.2", "react-native-gesture-handler": "~2.14.0", "react-native-reanimated": "~3.6.2", "expo-vector-icons": "^14.0.0", "expo-location": "~16.5.5", "expo-image-picker": "~14.7.1", "expo-permissions": "~14.4.0", "expo-linear-gradient": "~12.7.2", "react-native-svg": "14.1.0", "expo-av": "~13.10.5", "axios": "^1.6.5", "react-native-dotenv": "^3.4.9", "expo-constants": "~15.4.5", "expo-status-bar": "~1.11.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "babel-preset-expo": "~10.0.1", "eslint": "^8.57.0", "jest": "^29.2.1", "jest-expo": "~50.0.4", "prettier": "^3.0.0", "typescript": "^5.1.3"}, "engines": {"node": ">=18"}, "private": true}
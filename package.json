{"name": "alcfront", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "lint": "eslint ."}, "dependencies": {"@expo/vector-icons": "~14.0.4", "@react-native-async-storage/async-storage": "1.23.1", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@reduxjs/toolkit": "^2.0.1", "axios": "^1.6.5", "expo": "~50.0.14", "expo-constants": "~17.0.8", "expo-font": "^13.3.2", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-location": "~18.0.10", "expo-status-bar": "~2.0.1", "react": "18.3.1", "react-native": "0.76.9", "react-native-dotenv": "^3.4.9", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-redux": "^9.0.4", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@react-native-community/cli-server-api": "^18.0.0", "@types/react": "~18.3.12", "babel-preset-expo": "~12.0.0", "eslint": "^8.57.0", "jest": "^29.2.1", "jest-expo": "~50.0.4", "prettier": "^3.0.0", "typescript": "^5.1.3"}, "engines": {"node": ">=18"}, "private": true}
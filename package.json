{"name": "alcfront", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "lint": "eslint ."}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@reduxjs/toolkit": "^2.0.1", "axios": "^1.6.5", "expo": "~52.0.0", "expo-constants": "~17.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-location": "~18.1.6", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-dotenv": "^3.4.9", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-redux": "^9.0.4", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "babel-preset-expo": "~13.0.0", "eslint": "^8.57.0", "jest": "^29.2.1", "jest-expo": "~50.0.4", "prettier": "^3.0.0", "typescript": "~5.8.3"}, "engines": {"node": ">=18"}, "private": true}
{"name": "alcfront", "version": "1.0.0", "main": "index.js", "scripts": {"start": "react-native start", "android": "react-native run-android", "ios": "react-native run-ios", "test": "jest", "lint": "eslint ."}, "dependencies": {"react": "18.2.0", "react-native": "0.73.6", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "@reduxjs/toolkit": "^2.0.1", "react-redux": "^9.0.4", "redux-persist": "^6.0.0", "@react-native-async-storage/async-storage": "^1.21.0", "react-native-screens": "^3.29.0", "react-native-safe-area-context": "^4.8.2", "react-native-gesture-handler": "^2.14.1", "react-native-reanimated": "^3.6.2", "react-native-vector-icons": "^10.0.3", "react-native-maps": "^1.10.0", "react-native-image-picker": "^7.1.0", "react-native-permissions": "^4.1.5", "react-native-geolocation-service": "^5.3.1", "axios": "^1.6.5", "react-native-dotenv": "^3.4.9", "react-native-linear-gradient": "^2.8.3", "react-native-svg": "^14.1.0", "lottie-react-native": "^6.4.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@react-native-community/cli": "^12.3.6", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "metro-react-native-babel-preset": "0.77.0", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "private": true}
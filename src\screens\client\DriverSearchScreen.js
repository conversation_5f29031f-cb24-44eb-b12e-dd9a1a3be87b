import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, RefreshControl } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { MaterialIcons as Icon } from '@expo/vector-icons';

import { <PERSON><PERSON>, Header, Card, DriverCard, LoadingSpinner } from '../../components';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';
import { getLocationWithPermission } from '../../services/locationService';
import { getNearbyDrivers } from '../../services/driverService';
import { requestTrip, getFareEstimate } from '../../services/tripService';

const DriverSearchScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useDispatch();

  const { currentBAC, urgency } = route.params || {};
  const { profile } = useSelector(state => state.user);

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [drivers, setDrivers] = useState([]);
  const [currentLocation, setCurrentLocation] = useState(null);
  const [viewMode, setViewMode] = useState('list'); // 'list' or 'map'
  const [selectedDriver, setSelectedDriver] = useState(null);
  const [fareEstimate, setFareEstimate] = useState(null);

  useEffect(() => {
    loadDrivers();
  }, []);

  const loadDrivers = async () => {
    try {
      setLoading(true);

      // Get current location
      const locationResult = await getLocationWithPermission();
      if (!locationResult.success) {
        Alert.alert('Location Required', 'Please enable location services to find nearby drivers.');
        return;
      }

      setCurrentLocation(locationResult.data);

      // Get nearby drivers
      const driversResult = await getNearbyDrivers(
        locationResult.data.latitude,
        locationResult.data.longitude,
        5000 // 5km radius
      );

      if (driversResult.success) {
        setDrivers(driversResult.data.drivers || []);
      } else {
        Alert.alert('Error', driversResult.error);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load drivers. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDrivers();
    setRefreshing(false);
  };

  const handleDriverSelect = async (driver) => {
    setSelectedDriver(driver);

    // Get fare estimate
    if (currentLocation) {
      try {
        const fareResult = await getFareEstimate(
          {
            latitude: currentLocation.latitude,
            longitude: currentLocation.longitude,
            address: 'Current Location'
          },
          {
            latitude: driver.location.latitude,
            longitude: driver.location.longitude,
            address: 'Destination'
          }
        );

        if (fareResult.success) {
          setFareEstimate(fareResult.data);
        }
      } catch (error) {
        console.warn('Failed to get fare estimate:', error);
      }
    }
  };

  const handleRequestTrip = async () => {
    if (!selectedDriver || !currentLocation) return;

    try {
      setLoading(true);

      const tripData = {
        pickupLocation: {
          latitude: currentLocation.latitude,
          longitude: currentLocation.longitude,
          address: 'Current Location'
        },
        destination: {
          latitude: selectedDriver.location.latitude,
          longitude: selectedDriver.location.longitude,
          address: 'Destination'
        },
        driverId: selectedDriver.id,
        paymentMethod: 'cash',
        urgency: urgency || 'normal',
        bacLevel: currentBAC || 0
      };

      const result = await requestTrip(tripData);

      if (result.success) {
        Alert.alert(
          'Trip Requested',
          'Your trip request has been sent to the driver. You will be notified when they respond.',
          [
            {
              text: 'OK',
              onPress: () => navigation.navigate('TripStatus', { tripId: result.data.trip.id })
            }
          ]
        );
      } else {
        Alert.alert('Request Failed', result.error);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to request trip. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCallDriver = (driver) => {
    Alert.alert(
      'Call Driver',
      `Do you want to call ${driver.name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Call', onPress: () => {
          // In a real app, this would initiate a phone call
          Alert.alert('Calling...', `Calling ${driver.name}`);
        }}
      ]
    );
  };

  const handleEmergencyRequest = () => {
    Alert.alert(
      'Emergency Request',
      'This will send an urgent request to all nearby drivers. Use only in emergencies.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Send Emergency Request', style: 'destructive', onPress: () => {
          // Handle emergency request
          Alert.alert('Emergency Request Sent', 'All nearby drivers have been notified of your emergency.');
        }}
      ]
    );
  };

  if (loading && !refreshing) {
    return (
      <SafeAreaView style={styles.container}>
        <Header title="Finding Drivers" showBackButton onBackPress={() => navigation.goBack()} />
        <LoadingSpinner />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="Available Drivers"
        subtitle={`${drivers.length} drivers nearby`}
        showBackButton
        onBackPress={() => navigation.goBack()}
        rightIcon={viewMode === 'list' ? 'map' : 'list'}
        onRightPress={() => setViewMode(viewMode === 'list' ? 'map' : 'list')}
      />

      {/* Safety Alert */}
      {currentBAC > 0.05 && (
        <Card style={styles.alertCard}>
          <View style={styles.alertHeader}>
            <Icon name="warning" size={24} color={COLORS.DANGER} />
            <Text style={styles.alertTitle}>Safety Alert</Text>
          </View>
          <Text style={styles.alertText}>
            Your BAC is {(currentBAC * 100).toFixed(2)}%, which is above the legal limit.
            Please find a safe ride home.
          </Text>
        </Card>
      )}

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          {drivers.length === 0 ? (
            <Card style={styles.noDriversCard}>
              <Icon name="directions-car-filled" size={48} color={COLORS.GRAY_MEDIUM} />
              <Text style={styles.noDriversTitle}>No Drivers Available</Text>
              <Text style={styles.noDriversText}>
                There are no drivers in your area right now. Try refreshing or check back later.
              </Text>
              <Button
                title="Refresh"
                onPress={onRefresh}
                variant="outline"
                style={styles.refreshButton}
              />
            </Card>
          ) : (
            <>
              {drivers.map((driver) => (
                <DriverCard
                  key={driver.id}
                  driver={driver}
                  onPress={() => handleDriverSelect(driver)}
                  onCall={() => handleCallDriver(driver)}
                  style={[
                    styles.driverCard,
                    selectedDriver?.id === driver.id && styles.selectedDriverCard
                  ]}
                />
              ))}
            </>
          )}
        </View>
      </ScrollView>

      {/* Bottom Actions */}
      <View style={styles.footer}>
        {currentBAC > 0.08 && (
          <Button
            title="Emergency Request"
            onPress={handleEmergencyRequest}
            variant="danger"
            style={styles.emergencyButton}
          />
        )}

        {selectedDriver && (
          <View style={styles.selectedDriverInfo}>
            <View style={styles.fareInfo}>
              <Text style={styles.fareLabel}>Estimated Fare:</Text>
              <Text style={styles.fareAmount}>
                {fareEstimate ? `${fareEstimate.fareEstimate.toFixed(2)} TND` : 'Calculating...'}
              </Text>
            </View>

            <Button
              title={`Request Trip with ${selectedDriver.name}`}
              onPress={handleRequestTrip}
              style={styles.requestButton}
            />
          </View>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.WHITE,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: SPACING.MD,
    paddingBottom: SPACING.XXL,
  },
  alertCard: {
    marginHorizontal: SPACING.MD,
    marginBottom: SPACING.SM,
    backgroundColor: COLORS.DANGER + '10',
    borderLeftWidth: 4,
    borderLeftColor: COLORS.DANGER,
  },
  alertHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  alertTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: 'bold',
    color: COLORS.DANGER,
    marginLeft: SPACING.SM,
  },
  alertText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.GRAY_DARK,
    lineHeight: 18,
  },
  noDriversCard: {
    alignItems: 'center',
    paddingVertical: SPACING.XXL,
  },
  noDriversTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.BLACK,
    marginTop: SPACING.MD,
    marginBottom: SPACING.SM,
  },
  noDriversText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.GRAY_MEDIUM,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: SPACING.LG,
  },
  refreshButton: {
    alignSelf: 'center',
  },
  driverCard: {
    marginBottom: SPACING.SM,
  },
  selectedDriverCard: {
    borderWidth: 2,
    borderColor: COLORS.PRIMARY,
  },
  footer: {
    padding: SPACING.MD,
    backgroundColor: COLORS.WHITE,
    borderTopWidth: 1,
    borderTopColor: COLORS.GRAY_LIGHT,
  },
  emergencyButton: {
    marginBottom: SPACING.SM,
  },
  selectedDriverInfo: {
    marginTop: SPACING.SM,
  },
  fareInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.MD,
    paddingHorizontal: SPACING.SM,
  },
  fareLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.GRAY_DARK,
  },
  fareAmount: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
  },
  requestButton: {
    marginBottom: SPACING.SM,
  },
});

export default DriverSearchScreen;

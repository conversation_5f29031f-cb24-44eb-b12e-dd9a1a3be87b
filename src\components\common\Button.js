import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ActivityIndicator } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';

const Button = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  style,
  textStyle,
  icon,
  ...props
}) => {
  const getButtonStyle = () => {
    const baseStyle = [styles.button, styles[size]];
    
    if (variant === 'primary') {
      return [...baseStyle, styles.primary];
    } else if (variant === 'secondary') {
      return [...baseStyle, styles.secondary];
    } else if (variant === 'outline') {
      return [...baseStyle, styles.outline];
    } else if (variant === 'danger') {
      return [...baseStyle, styles.danger];
    }
    
    return baseStyle;
  };

  const getTextStyle = () => {
    const baseTextStyle = [styles.text, styles[`${size}Text`]];
    
    if (variant === 'primary' || variant === 'danger') {
      return [...baseTextStyle, styles.whiteText];
    } else if (variant === 'secondary') {
      return [...baseTextStyle, styles.primaryText];
    } else if (variant === 'outline') {
      return [...baseTextStyle, styles.primaryText];
    }
    
    return baseTextStyle;
  };

  if (variant === 'primary' && !disabled) {
    return (
      <TouchableOpacity
        onPress={onPress}
        disabled={disabled || loading}
        style={[getButtonStyle(), style]}
        {...props}
      >
        <LinearGradient
          colors={[COLORS.PRIMARY, '#3b82f6']}
          style={styles.gradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
        >
          {loading ? (
            <ActivityIndicator color={COLORS.WHITE} size="small" />
          ) : (
            <>
              {icon && icon}
              <Text style={[getTextStyle(), textStyle]}>{title}</Text>
            </>
          )}
        </LinearGradient>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled || loading}
      style={[getButtonStyle(), disabled && styles.disabled, style]}
      {...props}
    >
      {loading ? (
        <ActivityIndicator 
          color={variant === 'primary' || variant === 'danger' ? COLORS.WHITE : COLORS.PRIMARY} 
          size="small" 
        />
      ) : (
        <>
          {icon && icon}
          <Text style={[getTextStyle(), textStyle]}>{title}</Text>
        </>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  gradient: {
    flex: 1,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    paddingHorizontal: SPACING.LG,
  },
  
  // Sizes
  small: {
    height: 36,
    paddingHorizontal: SPACING.MD,
  },
  medium: {
    height: 48,
    paddingHorizontal: SPACING.LG,
  },
  large: {
    height: 56,
    paddingHorizontal: SPACING.XL,
  },
  
  // Variants
  primary: {
    backgroundColor: COLORS.PRIMARY,
  },
  secondary: {
    backgroundColor: COLORS.GRAY_LIGHT,
  },
  outline: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: COLORS.PRIMARY,
  },
  danger: {
    backgroundColor: COLORS.DANGER,
  },
  disabled: {
    opacity: 0.5,
  },
  
  // Text styles
  text: {
    fontWeight: '600',
    textAlign: 'center',
  },
  smallText: {
    fontSize: FONT_SIZES.SM,
  },
  mediumText: {
    fontSize: FONT_SIZES.MD,
  },
  largeText: {
    fontSize: FONT_SIZES.LG,
  },
  whiteText: {
    color: COLORS.WHITE,
  },
  primaryText: {
    color: COLORS.PRIMARY,
  },
});

export default Button;

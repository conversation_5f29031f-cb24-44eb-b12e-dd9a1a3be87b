import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Import driver screens
import { 
  DriverHomeScreen,
  DriverAvailabilityScreen,
  RideRequestScreen,
  KYCUploadScreen
} from '../screens';

import { COLORS } from '../utils/constants';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

const DriverHomeStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="DriverHome" component={DriverHomeScreen} />
      <Stack.Screen name="DriverAvailability" component={DriverAvailabilityScreen} />
      <Stack.Screen name="RideRequest" component={RideRequestScreen} />
      <Stack.Screen name="KYCUpload" component={KYCUploadScreen} />
    </Stack.Navigator>
  );
};

const DriverNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Home') {
            iconName = 'home';
          } else if (route.name === 'Rides') {
            iconName = 'directions-car';
          } else if (route.name === 'Profile') {
            iconName = 'person';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: COLORS.PRIMARY,
        tabBarInactiveTintColor: COLORS.GRAY_MEDIUM,
        headerShown: false,
      })}
    >
      <Tab.Screen name="Home" component={DriverHomeStack} />
      <Tab.Screen name="Rides" component={DriverHomeScreen} />
      <Tab.Screen name="Profile" component={DriverHomeScreen} />
    </Tab.Navigator>
  );
};

export default DriverNavigator;

// App constants
export const USER_ROLES = {
  CLIENT: 'client',
  DRIVER: 'driver',
  ADMIN: 'admin',
};

export const TRIP_STATUS = {
  REQUESTED: 'requested',
  ACCEPTED: 'accepted',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
};

export const DRIVER_STATUS = {
  OFFLINE: 'offline',
  ONLINE: 'online',
  BUSY: 'busy',
};

export const KYC_STATUS = {
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected',
};

// BAC calculation constants
export const BAC_LIMIT_TUNISIA = 0.03; // 0.03% BAC limit in Tunisia

export const ALCOHOL_TYPES = {
  BEER: { name: 'Beer', alcoholContent: 0.05 },
  WINE: { name: 'Wine', alcoholContent: 0.12 },
  LIQUOR: { name: 'Liquor', alcoholContent: 0.40 },
  COCKTAIL: { name: 'Cocktail', alcoholContent: 0.15 },
};

// Gender constants for BAC calculation
export const GENDER = {
  MALE: 'male',
  FEMALE: 'female',
};

// Widmark formula constants
export const WIDMARK_CONSTANTS = {
  MALE: 0.68,
  FEMALE: 0.55,
};

// Colors
export const COLORS = {
  PRIMARY: '#1e40af',
  SECONDARY: '#64748b',
  SUCCESS: '#10b981',
  DANGER: '#ef4444',
  WARNING: '#f59e0b',
  WHITE: '#ffffff',
  BLACK: '#000000',
  GRAY_LIGHT: '#f8fafc',
  GRAY_MEDIUM: '#94a3b8',
  GRAY_DARK: '#475569',
};

// Spacing
export const SPACING = {
  XS: 4,
  SM: 8,
  MD: 16,
  LG: 24,
  XL: 32,
  XXL: 48,
};

// Font sizes
export const FONT_SIZES = {
  XS: 12,
  SM: 14,
  MD: 16,
  LG: 18,
  XL: 24,
  XXL: 32,
};

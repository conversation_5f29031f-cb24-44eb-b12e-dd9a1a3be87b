import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, RefreshControl } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { useSelector, useDispatch } from 'react-redux';
import { MaterialIcons as Icon } from '@expo/vector-icons';

import { <PERSON><PERSON>, Header, Card, BACMeter } from '../../components';
import { COLORS, FONT_SIZES, SPACING, BAC_LIMIT_TUNISIA } from '../../utils/constants';
import { calculateBAC, getBACStatus } from '../../utils/bacCalculator';

const ClientHomeScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();

  const { drinks, currentBAC } = useSelector(state => state.bac);
  const { profile } = useSelector(state => state.user);
  const { currentTrip } = useSelector(state => state.trip);

  const [refreshing, setRefreshing] = useState(false);
  const [liveBAC, setLiveBAC] = useState(currentBAC);

  useEffect(() => {
    // Calculate live BAC based on current drinks and time elapsed
    if (drinks.length > 0 && profile?.weight && profile?.gender) {
      const firstDrinkTime = new Date(drinks[0].timestamp);
      const hoursElapsed = (new Date() - firstDrinkTime) / (1000 * 60 * 60);
      const bacResult = calculateBAC(drinks, profile.weight, profile.gender, hoursElapsed);
      setLiveBAC(bacResult.bac);
    } else {
      setLiveBAC(0);
    }
  }, [drinks, profile]);

  const onRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const bacStatus = getBACStatus(liveBAC);
  const hasActiveDrinks = drinks.length > 0;
  const canDrive = liveBAC <= BAC_LIMIT_TUNISIA;

  const quickActions = [
    {
      id: 'add-drinks',
      title: 'Add Drinks',
      subtitle: 'Log your alcohol consumption',
      icon: '🍺',
      color: COLORS.PRIMARY,
      onPress: () => navigation.navigate('AlcoholEntry'),
    },
    {
      id: 'find-driver',
      title: 'Find Driver',
      subtitle: 'Get a safe ride home',
      icon: '🚗',
      color: COLORS.SUCCESS,
      onPress: () => navigation.navigate('DriverSearch'),
    },
    {
      id: 'safety-tips',
      title: 'Safety Tips',
      subtitle: 'Learn about responsible drinking',
      icon: '🛡️',
      color: COLORS.WARNING,
      onPress: () => {
        // Navigate to safety tips screen
      },
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="AlcFront"
        subtitle="Drive Safe, Arrive Safe"
        rightIcon="notifications"
        onRightPress={() => {
          // Navigate to notifications
        }}
      />

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          {/* Current BAC Status */}
          <Card style={styles.bacCard}>
            <Text style={styles.cardTitle}>Current BAC Status</Text>

            <View style={styles.bacContainer}>
              <BACMeter bac={liveBAC} size={160} />

              <View style={styles.bacInfo}>
                <View style={[styles.statusBadge, { backgroundColor: bacStatus.color }]}>
                  <Icon
                    name={canDrive ? 'check' : 'warning'}
                    size={16}
                    color={COLORS.WHITE}
                  />
                  <Text style={styles.statusBadgeText}>
                    {canDrive ? 'Safe' : 'Unsafe'}
                  </Text>
                </View>

                <Text style={styles.bacDescription}>
                  {canDrive
                    ? 'You are within the legal limit to drive'
                    : 'You are above the legal limit - do not drive'
                  }
                </Text>

                {hasActiveDrinks && (
                  <TouchableOpacity
                    style={styles.viewDetailsButton}
                    onPress={() => navigation.navigate('BACResult', {
                      bacResult: { bac: liveBAC, canDrive },
                      drinks,
                      hoursElapsed: drinks.length > 0 ?
                        ((new Date() - new Date(drinks[0].timestamp)) / (1000 * 60 * 60)).toFixed(1) :
                        '0'
                    })}
                  >
                    <Text style={styles.viewDetailsText}>View Details</Text>
                    <Icon name="arrow-forward" size={16} color={COLORS.PRIMARY} />
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </Card>

          {/* Active Trip */}
          {currentTrip && (
            <Card style={styles.tripCard}>
              <View style={styles.tripHeader}>
                <Icon name="directions-car" size={24} color={COLORS.SUCCESS} />
                <Text style={styles.tripTitle}>Active Trip</Text>
              </View>
              <Text style={styles.tripStatus}>Status: {currentTrip.status}</Text>
              <Button
                title="View Trip Details"
                onPress={() => navigation.navigate('TripStatus')}
                variant="outline"
                size="small"
                style={styles.tripButton}
              />
            </Card>
          )}

          {/* Quick Actions */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Quick Actions</Text>
            <View style={styles.actionsGrid}>
              {quickActions.map((action) => (
                <TouchableOpacity
                  key={action.id}
                  style={[styles.actionCard, { borderLeftColor: action.color }]}
                  onPress={action.onPress}
                >
                  <Text style={styles.actionIcon}>{action.icon}</Text>
                  <View style={styles.actionContent}>
                    <Text style={styles.actionTitle}>{action.title}</Text>
                    <Text style={styles.actionSubtitle}>{action.subtitle}</Text>
                  </View>
                  <Icon name="arrow-forward-ios" size={16} color={COLORS.GRAY_MEDIUM} />
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Recent Activity */}
          {hasActiveDrinks && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Recent Activity</Text>
              <Card style={styles.activityCard}>
                <View style={styles.activityHeader}>
                  <Icon name="local-bar" size={20} color={COLORS.PRIMARY} />
                  <Text style={styles.activityTitle}>
                    {drinks.reduce((total, drink) => total + drink.quantity, 0)} drinks logged
                  </Text>
                </View>
                <Text style={styles.activityTime}>
                  Started {Math.floor((new Date() - new Date(drinks[0].timestamp)) / (1000 * 60))} minutes ago
                </Text>
              </Card>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.WHITE,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: SPACING.MD,
    paddingBottom: SPACING.XXL,
  },
  bacCard: {
    marginBottom: SPACING.MD,
  },
  cardTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.BLACK,
    marginBottom: SPACING.MD,
  },
  bacContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bacInfo: {
    flex: 1,
    marginLeft: SPACING.MD,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
    borderRadius: 16,
    alignSelf: 'flex-start',
    marginBottom: SPACING.SM,
  },
  statusBadgeText: {
    color: COLORS.WHITE,
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
    marginLeft: SPACING.XS,
  },
  bacDescription: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.GRAY_DARK,
    lineHeight: 20,
    marginBottom: SPACING.SM,
  },
  viewDetailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewDetailsText: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.PRIMARY,
    fontWeight: '500',
    marginRight: SPACING.XS,
  },
  tripCard: {
    marginBottom: SPACING.MD,
    backgroundColor: COLORS.SUCCESS + '10',
  },
  tripHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  tripTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.SUCCESS,
    marginLeft: SPACING.SM,
  },
  tripStatus: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.GRAY_DARK,
    marginBottom: SPACING.MD,
    textTransform: 'capitalize',
  },
  tripButton: {
    alignSelf: 'flex-start',
  },
  section: {
    marginBottom: SPACING.XL,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.BLACK,
    marginBottom: SPACING.MD,
  },
  actionsGrid: {
    gap: SPACING.SM,
  },
  actionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    borderRadius: 12,
    padding: SPACING.MD,
    borderLeftWidth: 4,
    shadowColor: COLORS.BLACK,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  actionIcon: {
    fontSize: 24,
    marginRight: SPACING.MD,
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.BLACK,
    marginBottom: 2,
  },
  actionSubtitle: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.GRAY_MEDIUM,
  },
  activityCard: {
    backgroundColor: COLORS.GRAY_LIGHT,
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.XS,
  },
  activityTitle: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.BLACK,
    marginLeft: SPACING.SM,
  },
  activityTime: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.GRAY_MEDIUM,
  },
});

export default ClientHomeScreen;

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { useDispatch } from 'react-redux';
import { MaterialIcons as Icon } from '@expo/vector-icons';
import { COLORS, FONT_SIZES, SPACING, USER_ROLES } from '../../utils/constants';
import { setRoleSelected } from '../../store/slices/userSlice';

const RoleSelectionScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();

  const handleRoleSelection = (role) => {
    // Set role as selected in Redux store
    dispatch(setRoleSelected(true));

    // Navigate to appropriate main screen based on role
    if (role === USER_ROLES.CLIENT) {
      navigation.reset({
        index: 0,
        routes: [{ name: 'Client' }],
      });
    } else if (role === USER_ROLES.DRIVER) {
      navigation.reset({
        index: 0,
        routes: [{ name: 'Driver' }],
      });
    } else if (role === USER_ROLES.ADMIN) {
      navigation.reset({
        index: 0,
        routes: [{ name: 'Admin' }],
      });
    }
  };

  const roles = [
    {
      id: USER_ROLES.CLIENT,
      title: 'Client',
      description: 'Calculate BAC and find drivers',
      icon: 'person',
      color: COLORS.PRIMARY,
    },
    {
      id: USER_ROLES.DRIVER,
      title: 'Driver',
      description: 'Provide ride services',
      icon: 'directions-car',
      color: COLORS.SUCCESS,
    },
    {
      id: USER_ROLES.ADMIN,
      title: 'Admin',
      description: 'Manage users and trips',
      icon: 'admin-panel-settings',
      color: COLORS.WARNING,
    },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Choose Your Role</Text>
        <Text style={styles.subtitle}>
          Select how you want to use AlcFront
        </Text>

        <View style={styles.rolesContainer}>
          {roles.map((role) => (
            <TouchableOpacity
              key={role.id}
              style={[styles.roleCard, { borderColor: role.color }]}
              onPress={() => handleRoleSelection(role.id)}
            >
              <Icon name={role.icon} size={48} color={role.color} />
              <Text style={styles.roleTitle}>{role.title}</Text>
              <Text style={styles.roleDescription}>{role.description}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.WHITE,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.XL,
    paddingTop: SPACING.XXL,
  },
  title: {
    fontSize: FONT_SIZES.XXL,
    fontWeight: 'bold',
    color: COLORS.BLACK,
    textAlign: 'center',
    marginBottom: SPACING.SM,
  },
  subtitle: {
    fontSize: FONT_SIZES.LG,
    color: COLORS.GRAY_MEDIUM,
    textAlign: 'center',
    marginBottom: SPACING.XXL,
  },
  rolesContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  roleCard: {
    backgroundColor: COLORS.WHITE,
    borderWidth: 2,
    borderRadius: 16,
    padding: SPACING.XL,
    marginBottom: SPACING.LG,
    alignItems: 'center',
    shadowColor: COLORS.BLACK,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  roleTitle: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
    color: COLORS.BLACK,
    marginTop: SPACING.MD,
    marginBottom: SPACING.SM,
  },
  roleDescription: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.GRAY_MEDIUM,
    textAlign: 'center',
  },
});

export default RoleSelectionScreen;

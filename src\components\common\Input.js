import React, { useState } from 'react';
import { View, TextInput, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';

const Input = ({
  label,
  placeholder,
  value,
  onChangeText,
  error,
  secureTextEntry = false,
  keyboardType = 'default',
  multiline = false,
  numberOfLines = 1,
  leftIcon,
  rightIcon,
  onRightIconPress,
  style,
  inputStyle,
  disabled = false,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(!secureTextEntry);

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  return (
    <View style={[styles.container, style]}>
      {label && <Text style={styles.label}>{label}</Text>}
      
      <View style={[
        styles.inputContainer,
        isFocused && styles.focused,
        error && styles.error,
        disabled && styles.disabled
      ]}>
        {leftIcon && (
          <View style={styles.leftIconContainer}>
            <Icon name={leftIcon} size={20} color={COLORS.GRAY_MEDIUM} />
          </View>
        )}
        
        <TextInput
          style={[
            styles.input,
            leftIcon && styles.inputWithLeftIcon,
            (rightIcon || secureTextEntry) && styles.inputWithRightIcon,
            inputStyle
          ]}
          placeholder={placeholder}
          placeholderTextColor={COLORS.GRAY_MEDIUM}
          value={value}
          onChangeText={onChangeText}
          secureTextEntry={secureTextEntry && !isPasswordVisible}
          keyboardType={keyboardType}
          multiline={multiline}
          numberOfLines={numberOfLines}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          editable={!disabled}
          {...props}
        />
        
        {secureTextEntry && (
          <TouchableOpacity
            style={styles.rightIconContainer}
            onPress={togglePasswordVisibility}
          >
            <Icon
              name={isPasswordVisible ? 'visibility-off' : 'visibility'}
              size={20}
              color={COLORS.GRAY_MEDIUM}
            />
          </TouchableOpacity>
        )}
        
        {rightIcon && !secureTextEntry && (
          <TouchableOpacity
            style={styles.rightIconContainer}
            onPress={onRightIconPress}
          >
            <Icon name={rightIcon} size={20} color={COLORS.GRAY_MEDIUM} />
          </TouchableOpacity>
        )}
      </View>
      
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.MD,
  },
  label: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
    color: COLORS.BLACK,
    marginBottom: SPACING.XS,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.GRAY_MEDIUM,
    borderRadius: 12,
    backgroundColor: COLORS.WHITE,
    minHeight: 48,
  },
  focused: {
    borderColor: COLORS.PRIMARY,
    shadowColor: COLORS.PRIMARY,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 2,
  },
  error: {
    borderColor: COLORS.DANGER,
  },
  disabled: {
    backgroundColor: COLORS.GRAY_LIGHT,
    opacity: 0.7,
  },
  input: {
    flex: 1,
    fontSize: FONT_SIZES.MD,
    color: COLORS.BLACK,
    paddingHorizontal: SPACING.MD,
    paddingVertical: SPACING.SM,
  },
  inputWithLeftIcon: {
    paddingLeft: SPACING.XS,
  },
  inputWithRightIcon: {
    paddingRight: SPACING.XS,
  },
  leftIconContainer: {
    paddingLeft: SPACING.MD,
    paddingRight: SPACING.XS,
  },
  rightIconContainer: {
    paddingRight: SPACING.MD,
    paddingLeft: SPACING.XS,
  },
  errorText: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.DANGER,
    marginTop: SPACING.XS,
    marginLeft: SPACING.XS,
  },
});

export default Input;

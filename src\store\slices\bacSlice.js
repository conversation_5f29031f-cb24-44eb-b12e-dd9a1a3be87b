import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  currentBAC: 0,
  drinks: [],
  canDrive: true,
  lastCalculation: null,
  loading: false,
  error: null,
};

const bacSlice = createSlice({
  name: 'bac',
  initialState,
  reducers: {
    addDrink: (state, action) => {
      state.drinks.push(action.payload);
    },
    removeDrink: (state, action) => {
      state.drinks = state.drinks.filter((_, index) => index !== action.payload);
    },
    clearDrinks: (state) => {
      state.drinks = [];
    },
    setBACResult: (state, action) => {
      state.currentBAC = action.payload.bac;
      state.canDrive = action.payload.canDrive;
      state.lastCalculation = new Date().toISOString();
    },
    resetBAC: (state) => {
      state.currentBAC = 0;
      state.drinks = [];
      state.canDrive = true;
      state.lastCalculation = null;
      state.loading = false;
      state.error = null;
    },
  },
});

export const {
  addDrink,
  removeDrink,
  clearDrinks,
  setBACResult,
  resetBAC,
} = bacSlice.actions;

export default bacSlice.reducer;

import api from './api';

/**
 * BAC (Blood Alcohol Content) service for drink logging and BAC calculation
 */

// Get available drinks list
export const getDrinks = async () => {
  try {
    const response = await api.get('/drinks');
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to fetch drinks list.',
    };
  }
};

// Log drink consumption
export const logDrinkConsumption = async (userId, drinkData) => {
  try {
    const response = await api.post(`/users/${userId}/consumption`, drinkData);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to log drink consumption.',
    };
  }
};

// Get current BAC
export const getCurrentBAC = async (userId) => {
  try {
    const response = await api.get(`/users/${userId}/bac`);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get current BAC.',
    };
  }
};

// Get safety assessment
export const getSafetyAssessment = async (userId, latitude, longitude) => {
  try {
    const response = await api.get(`/users/${userId}/safety-assessment`, {
      params: { latitude, longitude }
    });
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get safety assessment.',
    };
  }
};

// Get safety tips
export const getSafetyTips = async (userId) => {
  try {
    const response = await api.get(`/users/${userId}/safety-tips`);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get safety tips.',
    };
  }
};

// Emergency request
export const createEmergencyRequest = async (userId, emergencyData) => {
  try {
    const response = await api.post(`/users/${userId}/emergency-request`, emergencyData);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to create emergency request.',
    };
  }
};

// Get consumption history
export const getConsumptionHistory = async (userId, limit = 10) => {
  try {
    const response = await api.get(`/users/${userId}/consumption-history`, {
      params: { limit }
    });
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get consumption history.',
    };
  }
};

// Clear consumption data
export const clearConsumptionData = async (userId) => {
  try {
    const response = await api.delete(`/users/${userId}/consumption`);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to clear consumption data.',
    };
  }
};

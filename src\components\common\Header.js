import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, StatusBar } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons as Icon } from '@expo/vector-icons';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';

const Header = ({
  title,
  subtitle,
  leftIcon,
  rightIcon,
  onLeftPress,
  onRightPress,
  backgroundColor = COLORS.WHITE,
  textColor = COLORS.BLACK,
  showBackButton = false,
  onBackPress,
  style,
  ...props
}) => {
  return (
    <>
      <StatusBar 
        barStyle={backgroundColor === COLORS.WHITE ? 'dark-content' : 'light-content'} 
        backgroundColor={backgroundColor} 
      />
      <SafeAreaView style={[styles.container, { backgroundColor }, style]} {...props}>
        <View style={styles.header}>
          {/* Left side */}
          <View style={styles.leftContainer}>
            {showBackButton && (
              <TouchableOpacity
                style={styles.iconButton}
                onPress={onBackPress}
              >
                <Icon name="arrow-back" size={24} color={textColor} />
              </TouchableOpacity>
            )}
            {leftIcon && !showBackButton && (
              <TouchableOpacity
                style={styles.iconButton}
                onPress={onLeftPress}
              >
                <Icon name={leftIcon} size={24} color={textColor} />
              </TouchableOpacity>
            )}
          </View>

          {/* Center */}
          <View style={styles.centerContainer}>
            <Text style={[styles.title, { color: textColor }]} numberOfLines={1}>
              {title}
            </Text>
            {subtitle && (
              <Text style={[styles.subtitle, { color: textColor }]} numberOfLines={1}>
                {subtitle}
              </Text>
            )}
          </View>

          {/* Right side */}
          <View style={styles.rightContainer}>
            {rightIcon && (
              <TouchableOpacity
                style={styles.iconButton}
                onPress={onRightPress}
              >
                <Icon name={rightIcon} size={24} color={textColor} />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    borderBottomWidth: 1,
    borderBottomColor: COLORS.GRAY_LIGHT,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.MD,
    paddingVertical: SPACING.SM,
    minHeight: 56,
  },
  leftContainer: {
    width: 40,
    alignItems: 'flex-start',
  },
  centerContainer: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: SPACING.SM,
  },
  rightContainer: {
    width: 40,
    alignItems: 'flex-end',
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: FONT_SIZES.LG,
    fontWeight: '600',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: FONT_SIZES.SM,
    opacity: 0.7,
    textAlign: 'center',
    marginTop: 2,
  },
});

export default Header;

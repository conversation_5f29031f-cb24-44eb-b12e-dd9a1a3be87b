import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';

const SplashScreen = () => {
  const navigation = useNavigation();

  useEffect(() => {
    // Simulate loading time
    const timer = setTimeout(() => {
      navigation.replace('Auth');
    }, 2000);

    return () => clearTimeout(timer);
  }, [navigation]);

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>AlcFront</Text>
        <Text style={styles.subtitle}>BAC Calculator & Driver Service</Text>
        <Text style={styles.tagline}>Drive Safe, Arrive Safe</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: SPACING.XL,
  },
  title: {
    fontSize: FONT_SIZES.XXL + 8,
    fontWeight: 'bold',
    color: COLORS.WHITE,
    marginBottom: SPACING.SM,
  },
  subtitle: {
    fontSize: FONT_SIZES.LG,
    color: COLORS.WHITE,
    textAlign: 'center',
    marginBottom: SPACING.MD,
    opacity: 0.9,
  },
  tagline: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.WHITE,
    textAlign: 'center',
    opacity: 0.8,
  },
});

export default SplashScreen;

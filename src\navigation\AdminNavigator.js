import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { MaterialIcons as Icon } from '@expo/vector-icons';

// Import admin screens
import { 
  AdminHomeScreen,
  UserManagementScreen,
  KYCApprovalScreen,
  TripMonitoringScreen
} from '../screens';

import { COLORS } from '../utils/constants';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

const AdminHomeStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="AdminHome" component={AdminHomeScreen} />
      <Stack.Screen name="UserManagement" component={UserManagementScreen} />
      <Stack.Screen name="KYCApproval" component={KYCApprovalScreen} />
      <Stack.Screen name="TripMonitoring" component={TripMonitoringScreen} />
    </Stack.Navigator>
  );
};

const AdminNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Dashboard') {
            iconName = 'dashboard';
          } else if (route.name === 'Users') {
            iconName = 'people';
          } else if (route.name === 'KYC') {
            iconName = 'verified-user';
          } else if (route.name === 'Trips') {
            iconName = 'directions-car';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: COLORS.PRIMARY,
        tabBarInactiveTintColor: COLORS.GRAY_MEDIUM,
        headerShown: false,
      })}
    >
      <Tab.Screen name="Dashboard" component={AdminHomeStack} />
      <Tab.Screen name="Users" component={UserManagementScreen} />
      <Tab.Screen name="KYC" component={KYCApprovalScreen} />
      <Tab.Screen name="Trips" component={TripMonitoringScreen} />
    </Tab.Navigator>
  );
};

export default AdminNavigator;

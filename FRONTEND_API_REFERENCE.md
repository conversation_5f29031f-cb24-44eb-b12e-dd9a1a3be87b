# Frontend API Quick Reference
## BAC Calculator & Driver Service

Quick reference for all API endpoints with request/response examples.

## 🔐 Authentication

### Sign Up
```http
POST /api/auth/signup
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "+21612345678",
  "firstName": "<PERSON>",
  "lastName": "<PERSON> Ali",
  "role": "client",
  "weight": 75,
  "gender": "male"
}
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "role": "client",
    "firstName": "<PERSON>",
    "lastName": "<PERSON>"
  },
  "message": "User created successfully"
}
```

### Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

## 🍺 BAC & Drinks

### Get Drink Catalog
```http
GET /api/drinks/catalog?category=beer
```

**Response:**
```json
{
  "drinks": [
    {
      "id": "drink_id",
      "name": "Celtia Beer",
      "category": "beer",
      "alcoholPercentage": 5.0,
      "standardServing": 330,
      "description": "Popular Tunisian beer"
    }
  ],
  "categories": ["beer", "wine", "spirits", "cocktail", "other"]
}
```

### Log Drink Consumption
```http
POST /api/users/{userId}/consumption
Authorization: Bearer {token}
Content-Type: application/json

{
  "drinkId": "drink_object_id",
  "quantity": 330
}
```

### Get Current BAC
```http
GET /api/users/{userId}/bac
Authorization: Bearer {token}
```

**Response:**
```json
{
  "currentBAC": 0.08,
  "isLegalToDrive": false,
  "timeUntilLegal": 2.5,
  "status": "illegal",
  "message": "Your BAC is above the legal limit. Wait 2.5 hour(s) or find a driver",
  "legalLimit": 0.05,
  "consumptionSummary": {
    "totalDrinks": 3,
    "totalAlcoholGrams": 25.6,
    "hoursElapsed": 1.5
  }
}
```

### Get Safety Assessment
```http
GET /api/users/{userId}/safety-assessment?latitude=36.8065&longitude=10.1815
Authorization: Bearer {token}
```

**Response:**
```json
{
  "bac": {
    "currentBAC": 0.08,
    "isLegalToDrive": false,
    "status": "illegal"
  },
  "safetyLevel": {
    "level": "illegal",
    "color": "#DC2626",
    "priority": 2,
    "description": "Above legal limit - Find alternative transport"
  },
  "alerts": [
    {
      "type": "warning",
      "title": "Above Legal Limit",
      "message": "Your BAC (8.00%) is above Tunisia's legal limit (5.0%).",
      "action": "Use our driver service or wait until sober",
      "priority": 2
    }
  ],
  "driverSuggestions": {
    "urgencyLevel": "high",
    "nearbyDrivers": [...],
    "bestDriver": {...},
    "safetyMessage": "Driving is illegal at your current BAC. Choose a safe ride home."
  },
  "fareEstimate": {
    "estimatedFare": 12.50,
    "distance": 5,
    "estimatedDuration": 25
  }
}
```

## 🚗 Trip Management

### Request Trip
```http
POST /api/trips/request
Authorization: Bearer {token}
Content-Type: application/json

{
  "pickupLocation": {
    "latitude": 36.8065,
    "longitude": 10.1815,
    "address": "Avenue Habib Bourguiba, Tunis"
  },
  "destination": {
    "latitude": 36.8485,
    "longitude": 10.1980,
    "address": "Carthage, Tunisia"
  },
  "paymentMethod": "cash"
}
```

**Response:**
```json
{
  "message": "Trip requested successfully",
  "trip": {
    "id": "trip_id",
    "status": "requested",
    "fare": 12.50,
    "estimatedDuration": 25,
    "clientBAC": 0.08
  },
  "safetyAlert": {
    "level": "illegal",
    "message": "Your BAC is above the legal limit",
    "urgency": "high"
  },
  "suggestedDrivers": {
    "nearbyDrivers": [
      {
        "id": "driver_id",
        "name": "Mohamed Trabelsi",
        "rating": 4.8,
        "distance": 2.3,
        "vehicle": {
          "make": "Peugeot",
          "model": "208",
          "color": "White",
          "plateNumber": "123TUN456"
        }
      }
    ],
    "bestDriver": {...}
  }
}
```

### Get Fare Estimate
```http
POST /api/trips/estimate-fare
Authorization: Bearer {token}
Content-Type: application/json

{
  "pickupLocation": {
    "latitude": 36.8065,
    "longitude": 10.1815
  },
  "destination": {
    "latitude": 36.8485,
    "longitude": 10.1980
  },
  "paymentMethod": "cash"
}
```

**Response:**
```json
{
  "fareEstimate": 12.50,
  "fareRange": {
    "min": 10.63,
    "max": 14.38
  },
  "breakdown": {
    "baseFare": 2.50,
    "distanceFare": 4.00,
    "timeFare": 3.75,
    "timeMultiplier": 1.0,
    "bacPenalty": 0.50,
    "platformFee": 0.30,
    "paymentFee": 0.00,
    "subtotal": 11.05
  },
  "distance": 5.0,
  "estimatedDuration": 25,
  "appliedMultipliers": [
    {
      "type": "safety_fee",
      "amount": 0.50,
      "description": "Safety fee for high BAC"
    }
  ],
  "currency": "TND"
}
```

### Get Active Trip
```http
GET /api/trips/active
Authorization: Bearer {token}
```

### Cancel Trip
```http
PUT /api/trips/{tripId}/cancel
Authorization: Bearer {token}
```

### Rate Trip
```http
POST /api/trips/{tripId}/rate
Authorization: Bearer {token}
Content-Type: application/json

{
  "rating": 5,
  "comment": "Excellent service, very professional driver!"
}
```

## 🚙 Driver Endpoints

### Complete Driver Registration
```http
POST /api/drivers/register
Authorization: Bearer {token}
Content-Type: multipart/form-data

licenseNumber: TN123456789
cinNumber: 12345678
vehicleInfo: {"make":"Peugeot","model":"208","year":2020,"plateNumber":"123TUN456","color":"White"}
licenseDocument: [file]
cinDocument: [file]
```

### Update Driver Location
```http
PUT /api/drivers/location
Authorization: Bearer {token}
Content-Type: application/json

{
  "latitude": 36.8065,
  "longitude": 10.1815
}
```

### Toggle Driver Status
```http
PUT /api/drivers/status
Authorization: Bearer {token}
Content-Type: application/json

{
  "isOnline": true
}
```

### Get Available Trips (Driver)
```http
GET /api/trips/available?radius=10
Authorization: Bearer {token}
```

### Accept Trip (Driver)
```http
PUT /api/trips/{tripId}/accept
Authorization: Bearer {token}
```

### Start Trip (Driver)
```http
PUT /api/trips/{tripId}/start
Authorization: Bearer {token}
```

### Complete Trip (Driver)
```http
PUT /api/trips/{tripId}/complete
Authorization: Bearer {token}
```

## 🛡️ Safety & Emergency

### Emergency Request
```http
POST /api/users/{userId}/emergency-request
Authorization: Bearer {token}
Content-Type: application/json

{
  "latitude": 36.8065,
  "longitude": 10.1815,
  "emergencyType": "transport",
  "message": "Need safe transportation, BAC too high"
}
```

**Response:**
```json
{
  "message": "Emergency request submitted successfully",
  "emergencyId": "EMG-**********",
  "emergencyContacts": [
    {
      "service": "Police",
      "number": "190",
      "description": "Emergency police services"
    },
    {
      "service": "Medical Emergency",
      "number": "198",
      "description": "Medical emergency and ambulance"
    }
  ],
  "driverSuggestions": {...},
  "nextSteps": [
    "Accept driver assistance from our platform",
    "Share your location with a trusted contact",
    "Wait in a safe location for pickup"
  ]
}
```

### Get Safety Tips
```http
GET /api/users/{userId}/safety-tips
Authorization: Bearer {token}
```

## 📊 Common Response Patterns

### Success Response
```json
{
  "message": "Operation successful",
  "data": {...}
}
```

### Error Response
```json
{
  "error": "Error message",
  "details": "Additional error details"
}
```

### Validation Error
```json
{
  "errors": [
    {
      "field": "email",
      "message": "Invalid email format"
    }
  ]
}
```

## 🔄 Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `500` - Internal Server Error

## 🌍 Tunisia-Specific Constants

```javascript
// Constants for your app
export const TUNISIA_CONSTANTS = {
  LEGAL_BAC_LIMIT: 0.05,
  CURRENCY: 'TND',
  EMERGENCY_NUMBERS: {
    POLICE: '190',
    MEDICAL: '198',
    FIRE: '198',
    TRAFFIC: '197'
  },
  DEFAULT_LOCATION: {
    latitude: 36.8065,
    longitude: 10.1815,
    name: 'Tunis'
  }
};
```

## 🔧 Error Handling Examples

```javascript
// Handle API errors in your components
const handleApiCall = async () => {
  try {
    const response = await apiClient.get('/users/123/bac');
    setBacData(response);
  } catch (error) {
    if (error.message.includes('401')) {
      // Redirect to login
      navigation.navigate('Login');
    } else if (error.message.includes('Network')) {
      showToast('Please check your internet connection');
    } else {
      showToast(error.message || 'Something went wrong');
    }
  }
};
```

## 📱 Push Notification Payloads (Future)

When implementing push notifications, expect these payload structures:

```json
{
  "type": "TRIP_ACCEPTED",
  "data": {
    "tripId": "trip_id",
    "driverName": "Mohamed Trabelsi",
    "eta": 5
  }
}

{
  "type": "BAC_WARNING",
  "data": {
    "currentBAC": 0.06,
    "message": "Your BAC is above the legal limit"
  }
}

{
  "type": "SAFETY_ALERT",
  "data": {
    "alertType": "emergency",
    "message": "Emergency assistance requested"
  }
}
```

This reference covers all the essential endpoints your React Native app will need to integrate with the BAC Calculator backend! 🚀

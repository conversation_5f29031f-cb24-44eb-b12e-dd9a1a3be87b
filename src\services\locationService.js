import Geolocation from 'react-native-geolocation-service';
import { PermissionsAndroid, Platform, Alert } from 'react-native';

/**
 * Location service for handling GPS and location-related functionality
 */

// Request location permissions
export const requestLocationPermission = async () => {
  try {
    if (Platform.OS === 'android') {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        {
          title: 'Location Permission',
          message: '<PERSON>c<PERSON><PERSON><PERSON> needs access to your location to find nearby drivers and provide accurate services.',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        }
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    } else {
      // iOS permissions are handled automatically by react-native-geolocation-service
      return true;
    }
  } catch (error) {
    console.warn('Location permission error:', error);
    return false;
  }
};

// Get current location
export const getCurrentLocation = () => {
  return new Promise((resolve, reject) => {
    Geolocation.getCurrentPosition(
      (position) => {
        resolve({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          timestamp: position.timestamp,
        });
      },
      (error) => {
        console.warn('Location error:', error);
        reject(error);
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 10000,
      }
    );
  });
};

// Watch location changes
export const watchLocation = (callback, errorCallback) => {
  return Geolocation.watchPosition(
    (position) => {
      callback({
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy,
        timestamp: position.timestamp,
      });
    },
    (error) => {
      console.warn('Location watch error:', error);
      if (errorCallback) errorCallback(error);
    },
    {
      enableHighAccuracy: true,
      distanceFilter: 10, // Update every 10 meters
      interval: 5000, // Update every 5 seconds
      fastestInterval: 2000, // Fastest update every 2 seconds
    }
  );
};

// Stop watching location
export const stopWatchingLocation = (watchId) => {
  if (watchId !== null) {
    Geolocation.clearWatch(watchId);
  }
};

// Calculate distance between two points (Haversine formula)
export const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371; // Earth's radius in kilometers
  const dLat = toRadians(lat2 - lat1);
  const dLon = toRadians(lon2 - lon1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c; // Distance in kilometers
};

// Convert degrees to radians
const toRadians = (degrees) => {
  return degrees * (Math.PI / 180);
};

// Get location with permission check
export const getLocationWithPermission = async () => {
  try {
    const hasPermission = await requestLocationPermission();
    
    if (!hasPermission) {
      throw new Error('Location permission denied');
    }

    const location = await getCurrentLocation();
    return { success: true, data: location };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Failed to get location',
    };
  }
};

// Format coordinates for display
export const formatCoordinates = (latitude, longitude) => {
  return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
};

// Check if location is valid
export const isValidLocation = (latitude, longitude) => {
  return (
    typeof latitude === 'number' &&
    typeof longitude === 'number' &&
    latitude >= -90 &&
    latitude <= 90 &&
    longitude >= -180 &&
    longitude <= 180
  );
};

// Get address from coordinates (reverse geocoding)
// Note: This would typically use a geocoding service like Google Maps
export const getAddressFromCoordinates = async (latitude, longitude) => {
  try {
    // This is a placeholder - in a real app, you'd use a geocoding service
    return {
      success: true,
      data: {
        address: `${latitude.toFixed(4)}, ${longitude.toFixed(4)}`,
        city: 'Tunis',
        country: 'Tunisia',
      },
    };
  } catch (error) {
    return {
      success: false,
      error: 'Failed to get address from coordinates',
    };
  }
};

// Show location error alert
export const showLocationErrorAlert = (error) => {
  let title = 'Location Error';
  let message = 'Unable to get your location. Please try again.';

  switch (error.code) {
    case 1: // PERMISSION_DENIED
      title = 'Location Permission Required';
      message = 'Please enable location permissions in your device settings to use this feature.';
      break;
    case 2: // POSITION_UNAVAILABLE
      title = 'Location Unavailable';
      message = 'Your location is currently unavailable. Please check your GPS settings.';
      break;
    case 3: // TIMEOUT
      title = 'Location Timeout';
      message = 'Location request timed out. Please try again.';
      break;
  }

  Alert.alert(title, message, [
    { text: 'OK', style: 'default' }
  ]);
};

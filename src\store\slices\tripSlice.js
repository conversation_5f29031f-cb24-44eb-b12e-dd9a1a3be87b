import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  currentTrip: null,
  tripHistory: [],
  loading: false,
  error: null,
};

const tripSlice = createSlice({
  name: 'trip',
  initialState,
  reducers: {
    setCurrentTrip: (state, action) => {
      state.currentTrip = action.payload;
    },
    updateTripStatus: (state, action) => {
      if (state.currentTrip) {
        state.currentTrip.status = action.payload;
      }
    },
    addToTripHistory: (state, action) => {
      state.tripHistory.unshift(action.payload);
    },
    clearCurrentTrip: (state) => {
      if (state.currentTrip) {
        state.tripHistory.unshift(state.currentTrip);
        state.currentTrip = null;
      }
    },
    clearTripData: (state) => {
      state.currentTrip = null;
      state.tripHistory = [];
      state.loading = false;
      state.error = null;
    },
  },
});

export const {
  setCurrentTrip,
  updateTripStatus,
  addToTripHistory,
  clearCurrentTrip,
  clearTripData,
} = tripSlice.actions;

export default tripSlice.reducer;

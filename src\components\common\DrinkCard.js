import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { MaterialIcons as Icon } from '@expo/vector-icons';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';
import Card from './Card';

const DrinkCard = ({
  drinkType,
  quantity,
  alcoholContent,
  onIncrease,
  onDecrease,
  onRemove,
  style,
}) => {
  const getDrinkIcon = (type) => {
    switch (type.toLowerCase()) {
      case 'beer':
        return '🍺';
      case 'wine':
        return '🍷';
      case 'liquor':
        return '🥃';
      case 'cocktail':
        return '🍹';
      default:
        return '🥤';
    }
  };

  return (
    <Card style={[styles.container, style]} padding="medium">
      <View style={styles.header}>
        <View style={styles.drinkInfo}>
          <Text style={styles.drinkIcon}>{getDrinkIcon(drinkType)}</Text>
          <View style={styles.drinkDetails}>
            <Text style={styles.drinkName}>{drinkType}</Text>
            <Text style={styles.alcoholContent}>
              {(alcoholContent * 100).toFixed(0)}% alcohol
            </Text>
          </View>
        </View>
        
        <TouchableOpacity onPress={onRemove} style={styles.removeButton}>
          <Icon name="close" size={20} color={COLORS.DANGER} />
        </TouchableOpacity>
      </View>
      
      <View style={styles.quantityContainer}>
        <TouchableOpacity
          onPress={onDecrease}
          style={[styles.quantityButton, quantity <= 1 && styles.disabledButton]}
          disabled={quantity <= 1}
        >
          <Icon name="remove" size={20} color={quantity <= 1 ? COLORS.GRAY_MEDIUM : COLORS.PRIMARY} />
        </TouchableOpacity>
        
        <View style={styles.quantityDisplay}>
          <Text style={styles.quantityText}>{quantity}</Text>
          <Text style={styles.quantityLabel}>drinks</Text>
        </View>
        
        <TouchableOpacity onPress={onIncrease} style={styles.quantityButton}>
          <Icon name="add" size={20} color={COLORS.PRIMARY} />
        </TouchableOpacity>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.SM,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.MD,
  },
  drinkInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  drinkIcon: {
    fontSize: 32,
    marginRight: SPACING.SM,
  },
  drinkDetails: {
    flex: 1,
  },
  drinkName: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.BLACK,
    textTransform: 'capitalize',
  },
  alcoholContent: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.GRAY_MEDIUM,
    marginTop: 2,
  },
  removeButton: {
    padding: SPACING.XS,
    borderRadius: 16,
    backgroundColor: COLORS.GRAY_LIGHT,
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: COLORS.GRAY_LIGHT,
    alignItems: 'center',
    justifyContent: 'center',
  },
  disabledButton: {
    opacity: 0.5,
  },
  quantityDisplay: {
    alignItems: 'center',
    marginHorizontal: SPACING.LG,
    minWidth: 60,
  },
  quantityText: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
  },
  quantityLabel: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.GRAY_MEDIUM,
    marginTop: 2,
  },
});

export default DrinkCard;

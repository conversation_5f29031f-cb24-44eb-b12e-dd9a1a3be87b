import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { <PERSON><PERSON>, <PERSON>er, Card, DrinkCard } from '../../components';
import { COLORS, FONT_SIZES, SPACING, ALCOHOL_TYPES } from '../../utils/constants';
import { addDrink, removeDrink, clearDrinks } from '../../store/slices/bacSlice';
import { calculateBAC } from '../../utils/bacCalculator';

const AlcoholEntryScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();

  const { drinks } = useSelector(state => state.bac);
  const { profile } = useSelector(state => state.user);

  const [selectedDrinkType, setSelectedDrinkType] = useState(null);

  const drinkTypes = Object.entries(ALCOHOL_TYPES).map(([key, value]) => ({
    id: key,
    name: value.name,
    alcoholContent: value.alcoholContent,
  }));

  const handleAddDrink = (drinkType) => {
    const newDrink = {
      id: Date.now().toString(),
      type: drinkType.id.toLowerCase(),
      quantity: 1,
      timestamp: new Date().toISOString(),
    };

    dispatch(addDrink(newDrink));
  };

  const handleIncreaseDrink = (drinkIndex) => {
    const updatedDrinks = [...drinks];
    updatedDrinks[drinkIndex].quantity += 1;

    // Remove and re-add to update the store
    dispatch(removeDrink(drinkIndex));
    dispatch(addDrink(updatedDrinks[drinkIndex]));
  };

  const handleDecreaseDrink = (drinkIndex) => {
    const drink = drinks[drinkIndex];
    if (drink.quantity > 1) {
      const updatedDrink = { ...drink, quantity: drink.quantity - 1 };
      dispatch(removeDrink(drinkIndex));
      dispatch(addDrink(updatedDrink));
    }
  };

  const handleRemoveDrink = (drinkIndex) => {
    dispatch(removeDrink(drinkIndex));
  };

  const handleCalculateBAC = () => {
    if (drinks.length === 0) {
      Alert.alert('No Drinks Added', 'Please add at least one drink to calculate your BAC.');
      return;
    }

    if (!profile || !profile.weight || !profile.gender) {
      Alert.alert(
        'Profile Incomplete',
        'Please complete your profile (weight and gender) to calculate BAC.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Complete Profile', onPress: () => navigation.navigate('Profile') }
        ]
      );
      return;
    }

    // Calculate time elapsed since first drink
    const firstDrinkTime = new Date(drinks[0].timestamp);
    const hoursElapsed = (new Date() - firstDrinkTime) / (1000 * 60 * 60);

    const bacResult = calculateBAC(drinks, profile.weight, profile.gender, hoursElapsed);

    navigation.navigate('BACResult', {
      bacResult,
      drinks,
      hoursElapsed: hoursElapsed.toFixed(1)
    });
  };

  const handleClearAll = () => {
    Alert.alert(
      'Clear All Drinks',
      'Are you sure you want to remove all drinks?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Clear All', style: 'destructive', onPress: () => dispatch(clearDrinks()) }
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="Add Drinks"
        subtitle="Track your alcohol consumption"
        showBackButton
        onBackPress={() => navigation.goBack()}
        rightIcon={drinks.length > 0 ? "clear-all" : null}
        onRightPress={drinks.length > 0 ? handleClearAll : null}
      />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* Drink Type Selection */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Select Drink Type</Text>
            <View style={styles.drinkTypesGrid}>
              {drinkTypes.map((drinkType) => (
                <TouchableOpacity
                  key={drinkType.id}
                  style={styles.drinkTypeCard}
                  onPress={() => handleAddDrink(drinkType)}
                >
                  <Text style={styles.drinkTypeIcon}>
                    {drinkType.name === 'Beer' ? '🍺' :
                     drinkType.name === 'Wine' ? '🍷' :
                     drinkType.name === 'Liquor' ? '🥃' : '🍹'}
                  </Text>
                  <Text style={styles.drinkTypeName}>{drinkType.name}</Text>
                  <Text style={styles.drinkTypeAlcohol}>
                    {(drinkType.alcoholContent * 100).toFixed(0)}% alcohol
                  </Text>
                  <Icon name="add" size={20} color={COLORS.PRIMARY} style={styles.addIcon} />
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Added Drinks */}
          {drinks.length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Your Drinks ({drinks.length})</Text>
              {drinks.map((drink, index) => (
                <DrinkCard
                  key={`${drink.id}-${index}`}
                  drinkType={drink.type}
                  quantity={drink.quantity}
                  alcoholContent={ALCOHOL_TYPES[drink.type.toUpperCase()]?.alcoholContent || 0}
                  onIncrease={() => handleIncreaseDrink(index)}
                  onDecrease={() => handleDecreaseDrink(index)}
                  onRemove={() => handleRemoveDrink(index)}
                />
              ))}
            </View>
          )}

          {/* Summary */}
          {drinks.length > 0 && (
            <Card style={styles.summaryCard}>
              <Text style={styles.summaryTitle}>Summary</Text>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Total Drinks:</Text>
                <Text style={styles.summaryValue}>
                  {drinks.reduce((total, drink) => total + drink.quantity, 0)}
                </Text>
              </View>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Time Since First Drink:</Text>
                <Text style={styles.summaryValue}>
                  {drinks.length > 0 ?
                    `${Math.max(0, ((new Date() - new Date(drinks[0].timestamp)) / (1000 * 60))).toFixed(0)} min` :
                    '0 min'
                  }
                </Text>
              </View>
            </Card>
          )}
        </View>
      </ScrollView>

      {/* Calculate BAC Button */}
      {drinks.length > 0 && (
        <View style={styles.footer}>
          <Button
            title="Calculate My BAC"
            onPress={handleCalculateBAC}
            style={styles.calculateButton}
          />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.WHITE,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: SPACING.MD,
    paddingBottom: SPACING.XXL,
  },
  section: {
    marginBottom: SPACING.XL,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.BLACK,
    marginBottom: SPACING.MD,
  },
  drinkTypesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  drinkTypeCard: {
    width: '48%',
    backgroundColor: COLORS.WHITE,
    borderRadius: 16,
    padding: SPACING.MD,
    marginBottom: SPACING.SM,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.GRAY_LIGHT,
    position: 'relative',
    shadowColor: COLORS.BLACK,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  drinkTypeIcon: {
    fontSize: 32,
    marginBottom: SPACING.XS,
  },
  drinkTypeName: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.BLACK,
    marginBottom: SPACING.XS,
  },
  drinkTypeAlcohol: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.GRAY_MEDIUM,
    marginBottom: SPACING.SM,
  },
  addIcon: {
    position: 'absolute',
    top: SPACING.SM,
    right: SPACING.SM,
  },
  summaryCard: {
    backgroundColor: COLORS.GRAY_LIGHT,
    marginTop: SPACING.MD,
  },
  summaryTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.BLACK,
    marginBottom: SPACING.MD,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  summaryLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.GRAY_DARK,
  },
  summaryValue: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.PRIMARY,
  },
  footer: {
    padding: SPACING.MD,
    backgroundColor: COLORS.WHITE,
    borderTopWidth: 1,
    borderTopColor: COLORS.GRAY_LIGHT,
  },
  calculateButton: {
    marginBottom: SPACING.SM,
  },
});

export default AlcoholEntryScreen;

/**
 * Validation utilities for form inputs
 */

// Email validation
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!email) {
    return 'Email is required';
  }
  if (!emailRegex.test(email)) {
    return 'Please enter a valid email address';
  }
  return null;
};

// Phone number validation (Tunisian format)
export const validatePhoneNumber = (phone) => {
  // Remove all non-digit characters
  const cleanPhone = phone.replace(/\D/g, '');
  
  if (!phone) {
    return 'Phone number is required';
  }
  
  // Tunisian phone numbers: +216 XX XXX XXX or 216XXXXXXXX or XXXXXXXX
  const tunisianPhoneRegex = /^(\+216|216)?[2-9]\d{7}$/;
  
  if (!tunisianPhoneRegex.test(cleanPhone)) {
    return 'Please enter a valid Tunisian phone number';
  }
  
  return null;
};

// Password validation
export const validatePassword = (password) => {
  if (!password) {
    return 'Password is required';
  }
  if (password.length < 6) {
    return 'Password must be at least 6 characters long';
  }
  if (password.length > 50) {
    return 'Password must be less than 50 characters';
  }
  return null;
};

// Name validation
export const validateName = (name, fieldName = 'Name') => {
  if (!name) {
    return `${fieldName} is required`;
  }
  if (name.length < 2) {
    return `${fieldName} must be at least 2 characters long`;
  }
  if (name.length > 50) {
    return `${fieldName} must be less than 50 characters`;
  }
  if (!/^[a-zA-Z\s\u0600-\u06FF]+$/.test(name)) {
    return `${fieldName} can only contain letters and spaces`;
  }
  return null;
};

// Weight validation
export const validateWeight = (weight) => {
  const weightNum = parseFloat(weight);
  
  if (!weight) {
    return 'Weight is required';
  }
  if (isNaN(weightNum)) {
    return 'Weight must be a valid number';
  }
  if (weightNum < 30 || weightNum > 300) {
    return 'Weight must be between 30 and 300 kg';
  }
  return null;
};

// Age validation
export const validateAge = (age) => {
  const ageNum = parseInt(age);
  
  if (!age) {
    return 'Age is required';
  }
  if (isNaN(ageNum)) {
    return 'Age must be a valid number';
  }
  if (ageNum < 18 || ageNum > 100) {
    return 'Age must be between 18 and 100 years';
  }
  return null;
};

// Gender validation
export const validateGender = (gender) => {
  if (!gender) {
    return 'Gender is required';
  }
  if (!['male', 'female'].includes(gender.toLowerCase())) {
    return 'Please select a valid gender';
  }
  return null;
};

// OTP validation
export const validateOTP = (otp) => {
  if (!otp) {
    return 'Verification code is required';
  }
  if (!/^\d{4,6}$/.test(otp)) {
    return 'Verification code must be 4-6 digits';
  }
  return null;
};

// License plate validation (Tunisian format)
export const validateLicensePlate = (plate) => {
  if (!plate) {
    return 'License plate is required';
  }
  
  // Tunisian license plate format: TUN-123 or 123-TUN-456
  const tunisianPlateRegex = /^(TUN-\d{1,4}|\d{1,3}-TUN-\d{1,4})$/i;
  
  if (!tunisianPlateRegex.test(plate)) {
    return 'Please enter a valid Tunisian license plate (e.g., TUN-123)';
  }
  
  return null;
};

// Driver license number validation
export const validateDriverLicense = (license) => {
  if (!license) {
    return 'Driver license number is required';
  }
  if (license.length < 5 || license.length > 20) {
    return 'Driver license number must be between 5 and 20 characters';
  }
  return null;
};

// CIN (Carte d'Identité Nationale) validation
export const validateCIN = (cin) => {
  if (!cin) {
    return 'CIN number is required';
  }
  
  // Tunisian CIN format: 8 digits
  const cinRegex = /^\d{8}$/;
  
  if (!cinRegex.test(cin)) {
    return 'CIN must be exactly 8 digits';
  }
  
  return null;
};

// Generic required field validation
export const validateRequired = (value, fieldName) => {
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return `${fieldName} is required`;
  }
  return null;
};

// Validate form object
export const validateForm = (formData, validationRules) => {
  const errors = {};
  
  Object.keys(validationRules).forEach(field => {
    const rules = validationRules[field];
    const value = formData[field];
    
    for (const rule of rules) {
      const error = rule(value);
      if (error) {
        errors[field] = error;
        break; // Stop at first error for this field
      }
    }
  });
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

# Frontend Testing Guide
## BAC Calculator & Driver Service Integration

Complete testing checklist and scenarios for React Native integration.

## 🧪 Testing Setup

### Prerequisites
1. **Backend running** on `http://localhost:3000`
2. **Database seeded** with sample data (`npm run seed`)
3. **Postman collection** imported for API verification
4. **Test accounts** available (see credentials below)

### Test Accounts
```javascript
// Use these accounts for testing
const TEST_ACCOUNTS = {
  admin: {
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin'
  },
  client: {
    email: '<EMAIL>',
    password: 'client123',
    role: 'client',
    weight: 75,
    gender: 'male'
  },
  driver: {
    email: '<EMAIL>',
    password: 'driver123',
    role: 'driver',
    isApproved: true
  }
};
```

## 📋 Testing Checklist

### ✅ Authentication Flow
- [ ] **Client Signup**
  - [ ] Valid registration with weight/gender
  - [ ] Email validation
  - [ ] Phone number validation
  - [ ] Password strength validation
  - [ ] Duplicate email/phone handling

- [ ] **Driver Signup**
  - [ ] Basic registration
  - [ ] Document upload flow
  - [ ] Vehicle information entry
  - [ ] Approval pending state

- [ ] **Login/Logout**
  - [ ] Valid credentials
  - [ ] Invalid credentials
  - [ ] Token persistence
  - [ ] Auto-logout on token expiry

### ✅ BAC Calculation & Safety
- [ ] **Drink Logging**
  - [ ] Select drink from catalog
  - [ ] Enter custom quantity
  - [ ] Multiple drinks in session
  - [ ] Consumption history display

- [ ] **BAC Monitoring**
  - [ ] Real-time BAC calculation
  - [ ] Color-coded BAC display
  - [ ] Legal status indication
  - [ ] Time until legal calculation

- [ ] **Safety Features**
  - [ ] Automatic alerts at 0.03% BAC
  - [ ] Driver suggestions at 0.05% BAC
  - [ ] Emergency alerts at 0.15% BAC
  - [ ] Safety tips display

### ✅ Location & Maps
- [ ] **Location Permissions**
  - [ ] Request location permission
  - [ ] Handle permission denied
  - [ ] GPS accuracy handling
  - [ ] Location updates

- [ ] **Map Integration**
  - [ ] Display current location
  - [ ] Show nearby drivers
  - [ ] Route visualization
  - [ ] Address geocoding

### ✅ Trip Management (Client)
- [ ] **Trip Request**
  - [ ] Set pickup location
  - [ ] Set destination
  - [ ] Fare estimate display
  - [ ] Payment method selection
  - [ ] BAC-based safety warnings

- [ ] **Trip Tracking**
  - [ ] Active trip status
  - [ ] Driver information display
  - [ ] Real-time location updates
  - [ ] ETA calculations

- [ ] **Trip Completion**
  - [ ] Trip completion notification
  - [ ] Rating system
  - [ ] Payment confirmation
  - [ ] Trip history

### ✅ Driver Features
- [ ] **Driver Registration**
  - [ ] Document upload (license, CIN)
  - [ ] Vehicle information
  - [ ] Approval workflow
  - [ ] Status notifications

- [ ] **Driver Operations**
  - [ ] Online/offline toggle
  - [ ] Location updates
  - [ ] Available trips list
  - [ ] Trip acceptance

- [ ] **Trip Management (Driver)**
  - [ ] Accept trip requests
  - [ ] Navigate to pickup
  - [ ] Start trip
  - [ ] Complete trip
  - [ ] Rating clients

### ✅ Emergency Features
- [ ] **Emergency Requests**
  - [ ] Transport emergency
  - [ ] Medical emergency
  - [ ] Safety emergency
  - [ ] Location sharing

- [ ] **Emergency Contacts**
  - [ ] Tunisia emergency numbers
  - [ ] One-tap calling
  - [ ] Emergency information display

## 🧪 Test Scenarios

### Scenario 1: High BAC User Journey
```javascript
// Test the complete flow for a user with high BAC
const testHighBACFlow = async () => {
  // 1. Login as client
  await login('<EMAIL>', 'client123');
  
  // 2. Log multiple drinks to reach high BAC
  await logDrink('beer_id', 330); // First beer
  await logDrink('beer_id', 330); // Second beer
  await logDrink('spirits_id', 40); // Shot
  
  // 3. Check BAC - should be > 0.05%
  const bacData = await getCurrentBAC();
  expect(bacData.currentBAC).toBeGreaterThan(0.05);
  expect(bacData.isLegalToDrive).toBe(false);
  
  // 4. Get safety assessment - should show driver suggestions
  const safety = await getSafetyAssessment(userLocation);
  expect(safety.driverSuggestions).toBeDefined();
  expect(safety.alerts.length).toBeGreaterThan(0);
  
  // 5. Request trip - should show safety warnings
  const tripResponse = await requestTrip(pickup, destination);
  expect(tripResponse.safetyAlert).toBeDefined();
  expect(tripResponse.suggestedDrivers).toBeDefined();
};
```

### Scenario 2: Driver Workflow
```javascript
// Test complete driver workflow
const testDriverWorkflow = async () => {
  // 1. Login as driver
  await login('<EMAIL>', 'driver123');
  
  // 2. Go online
  await toggleDriverStatus(true);
  
  // 3. Update location
  await updateDriverLocation(36.8065, 10.1815);
  
  // 4. Get available trips
  const availableTrips = await getAvailableTrips();
  expect(availableTrips.length).toBeGreaterThan(0);
  
  // 5. Accept a trip
  const tripId = availableTrips[0].id;
  await acceptTrip(tripId);
  
  // 6. Start trip
  await startTrip(tripId);
  
  // 7. Complete trip
  await completeTrip(tripId);
  
  // 8. Rate client
  await rateTrip(tripId, 5, 'Great client!');
};
```

### Scenario 3: Emergency Situation
```javascript
// Test emergency request flow
const testEmergencyFlow = async () => {
  // 1. Login as client with high BAC
  await login('<EMAIL>', 'client123');
  
  // 2. Log excessive drinks (BAC > 0.15%)
  await logMultipleDrinks(); // Helper function
  
  // 3. Check BAC - should trigger emergency alerts
  const bacData = await getCurrentBAC();
  expect(bacData.currentBAC).toBeGreaterThan(0.15);
  
  // 4. Request emergency assistance
  const emergency = await requestEmergency(
    userLocation,
    'transport',
    'Need immediate help, very high BAC'
  );
  
  expect(emergency.emergencyContacts).toBeDefined();
  expect(emergency.nextSteps.length).toBeGreaterThan(0);
};
```

## 🔍 Edge Cases to Test

### Network Conditions
- [ ] **Offline Mode**
  - [ ] Handle no internet connection
  - [ ] Cache critical data
  - [ ] Queue requests for retry
  - [ ] Show offline indicators

- [ ] **Poor Connection**
  - [ ] Request timeouts
  - [ ] Retry mechanisms
  - [ ] Loading states
  - [ ] Error messages

### Data Validation
- [ ] **Invalid Inputs**
  - [ ] Negative BAC values
  - [ ] Invalid coordinates
  - [ ] Malformed phone numbers
  - [ ] Empty required fields

- [ ] **Boundary Values**
  - [ ] BAC exactly at 0.05%
  - [ ] Maximum trip distance
  - [ ] Minimum fare amounts
  - [ ] Location accuracy limits

### User Experience
- [ ] **Performance**
  - [ ] App startup time
  - [ ] API response times
  - [ ] Map rendering speed
  - [ ] Image loading

- [ ] **Accessibility**
  - [ ] Screen reader support
  - [ ] Color contrast
  - [ ] Font scaling
  - [ ] Touch targets

## 🐛 Common Issues & Solutions

### Authentication Issues
```javascript
// Handle token expiry
const handleTokenExpiry = (error) => {
  if (error.message.includes('401')) {
    // Clear stored token
    AsyncStorage.removeItem('authToken');
    // Redirect to login
    navigation.navigate('Login');
    showToast('Session expired. Please login again.');
  }
};
```

### Location Issues
```javascript
// Handle location permission denied
const handleLocationError = (error) => {
  if (error.code === 'PERMISSION_DENIED') {
    Alert.alert(
      'Location Required',
      'This app needs location access to find nearby drivers.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Settings', onPress: () => Linking.openSettings() }
      ]
    );
  }
};
```

### BAC Calculation Issues
```javascript
// Validate BAC calculation inputs
const validateBACInputs = (weight, gender) => {
  if (!weight || weight <= 0) {
    throw new Error('Valid weight is required for BAC calculation');
  }
  if (!gender || !['male', 'female'].includes(gender)) {
    throw new Error('Gender is required for BAC calculation');
  }
};
```

## 📊 Testing Tools & Libraries

### Recommended Testing Stack
```javascript
// Testing dependencies for package.json
{
  "jest": "^29.0.0",
  "@testing-library/react-native": "^12.0.0",
  "@testing-library/jest-native": "^5.0.0",
  "react-test-renderer": "^18.0.0",
  "detox": "^20.0.0", // E2E testing
  "flipper": "^0.200.0", // Debugging
  "prop-types": "^15.8.1" // For JavaScript prop validation
}
```

### Mock Services
```javascript
// __mocks__/apiClient.js
export const mockApiClient = {
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  setAuthToken: jest.fn(),
  request: jest.fn(),
};

// __mocks__/locationService.js
export const mockLocationService = {
  getCurrentLocation: jest.fn().mockResolvedValue({
    latitude: 36.8065,
    longitude: 10.1815,
  }),
  requestLocationPermission: jest.fn().mockResolvedValue(true),
  findNearbyDrivers: jest.fn().mockResolvedValue({
    drivers: [],
    stats: { totalDrivers: 0, onlineDrivers: 0, availableDrivers: 0 }
  }),
};

// __mocks__/bacService.js
export const mockBacService = {
  getCurrentBAC: jest.fn().mockResolvedValue({
    currentBAC: 0.05,
    isLegalToDrive: false,
    timeUntilLegal: 1.5,
    status: 'illegal',
    message: 'Above legal limit'
  }),
  getSafetyAssessment: jest.fn().mockResolvedValue({
    bac: { currentBAC: 0.05, isLegalToDrive: false },
    safetyLevel: { level: 'illegal', priority: 2 },
    alerts: [],
    driverSuggestions: null
  }),
  logDrink: jest.fn().mockResolvedValue({ success: true }),
  getDrinkCatalog: jest.fn().mockResolvedValue({
    drinks: [
      { id: '1', name: 'Beer', category: 'beer', alcoholPercentage: 5.0 }
    ]
  }),
};
```

## 📱 Device Testing

### Test on Multiple Devices
- [ ] **iOS Devices**
  - [ ] iPhone (various models)
  - [ ] iPad
  - [ ] Different iOS versions

- [ ] **Android Devices**
  - [ ] Various screen sizes
  - [ ] Different Android versions
  - [ ] Different manufacturers

### Platform-Specific Features
- [ ] **iOS**
  - [ ] Face ID/Touch ID integration
  - [ ] iOS location permissions
  - [ ] App Store guidelines compliance

- [ ] **Android**
  - [ ] Android location permissions
  - [ ] Background location updates
  - [ ] Google Play guidelines compliance

## 🚀 Performance Testing

### Key Metrics to Monitor
- [ ] **App Performance**
  - [ ] Cold start time < 3 seconds
  - [ ] API response time < 2 seconds
  - [ ] Map rendering < 1 second
  - [ ] Memory usage optimization

- [ ] **Battery Usage**
  - [ ] Location tracking efficiency
  - [ ] Background processing
  - [ ] Network request optimization

## 📋 Pre-Release Checklist

- [ ] All test scenarios pass
- [ ] No critical bugs
- [ ] Performance benchmarks met
- [ ] Accessibility compliance
- [ ] Security review completed
- [ ] App store guidelines followed
- [ ] Privacy policy implemented
- [ ] Terms of service included

## 🆘 Support & Debugging

### Debug Information to Collect
```javascript
// Debug info helper
const getDebugInfo = () => ({
  appVersion: '1.0.0',
  platform: Platform.OS,
  osVersion: Platform.Version,
  deviceModel: DeviceInfo.getModel(),
  apiBaseUrl: API_CONFIG.BASE_URL,
  userRole: currentUser?.role,
  lastAPICall: lastApiCallTimestamp,
  currentLocation: lastKnownLocation,
});
```

### Common Debug Commands
```bash
# React Native debugging
npx react-native log-android
npx react-native log-ios

# Flipper debugging
npx flipper

# Metro bundler
npx react-native start --reset-cache
```

This comprehensive testing guide ensures your React Native app integrates seamlessly with the BAC Calculator backend! 🧪📱

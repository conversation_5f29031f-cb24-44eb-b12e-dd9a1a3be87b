import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { COLORS, FONT_SIZES, SPACING, BAC_LIMIT_TUNISIA } from '../../utils/constants';

const BACMeter = ({ bac = 0, size = 200 }) => {
  const radius = (size - 20) / 2;
  const strokeWidth = 12;
  const normalizedRadius = radius - strokeWidth * 2;
  const circumference = normalizedRadius * 2 * Math.PI;
  
  // Calculate progress (0-1) based on BAC
  // We'll show full circle at 0.1% BAC for visual purposes
  const maxBAC = 0.1;
  const progress = Math.min(bac / maxBAC, 1);
  const strokeDasharray = `${circumference} ${circumference}`;
  const strokeDashoffset = circumference - progress * circumference;
  
  // Determine color based on BAC level
  const getColor = () => {
    if (bac <= BAC_LIMIT_TUNISIA) {
      return COLORS.SUCCESS;
    } else if (bac <= 0.05) {
      return COLORS.WARNING;
    } else {
      return COLORS.DANGER;
    }
  };

  const getStatus = () => {
    if (bac <= BAC_LIMIT_TUNISIA) {
      return 'Safe to Drive';
    } else {
      return 'Do Not Drive';
    }
  };

  return (
    <View style={[styles.container, { width: size, height: size }]}>
      {/* Circular progress using View */}
      <View style={[styles.circleBackground, { width: size, height: size, borderRadius: size / 2 }]}>
        <View
          style={[
            styles.circleProgress,
            {
              width: size - 20,
              height: size - 20,
              borderRadius: (size - 20) / 2,
              borderColor: getColor(),
              borderWidth: strokeWidth,
              transform: [{ rotate: `${progress * 360}deg` }]
            }
          ]}
        />
      </View>

      <View style={styles.content}>
        <Text style={styles.bacValue}>
          {(bac * 100).toFixed(2)}%
        </Text>
        <Text style={styles.bacLabel}>BAC</Text>
        <Text style={[styles.status, { color: getColor() }]}>
          {getStatus()}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  circleBackground: {
    position: 'absolute',
    borderWidth: 12,
    borderColor: COLORS.GRAY_LIGHT,
    backgroundColor: 'transparent',
  },
  circleProgress: {
    position: 'absolute',
    backgroundColor: 'transparent',
    borderTopColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: 'transparent',
    top: 10,
    left: 10,
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
  bacValue: {
    fontSize: FONT_SIZES.XXL + 8,
    fontWeight: 'bold',
    color: COLORS.BLACK,
  },
  bacLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.GRAY_MEDIUM,
    marginTop: -4,
  },
  status: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    marginTop: SPACING.XS,
    textAlign: 'center',
  },
});

export default BACMeter;

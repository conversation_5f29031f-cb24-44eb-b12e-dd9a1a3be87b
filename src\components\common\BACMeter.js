import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Svg, { Circle, Path } from 'react-native-svg';
import { COLORS, FONT_SIZES, SPACING, BAC_LIMIT_TUNISIA } from '../../utils/constants';

const BACMeter = ({ bac = 0, size = 200 }) => {
  const radius = (size - 20) / 2;
  const strokeWidth = 12;
  const normalizedRadius = radius - strokeWidth * 2;
  const circumference = normalizedRadius * 2 * Math.PI;
  
  // Calculate progress (0-1) based on BAC
  // We'll show full circle at 0.1% BAC for visual purposes
  const maxBAC = 0.1;
  const progress = Math.min(bac / maxBAC, 1);
  const strokeDasharray = `${circumference} ${circumference}`;
  const strokeDashoffset = circumference - progress * circumference;
  
  // Determine color based on BAC level
  const getColor = () => {
    if (bac <= BAC_LIMIT_TUNISIA) {
      return COLORS.SUCCESS;
    } else if (bac <= 0.05) {
      return COLORS.WARNING;
    } else {
      return COLORS.DANGER;
    }
  };

  const getStatus = () => {
    if (bac <= BAC_LIMIT_TUNISIA) {
      return 'Safe to Drive';
    } else {
      return 'Do Not Drive';
    }
  };

  return (
    <View style={[styles.container, { width: size, height: size }]}>
      <Svg width={size} height={size} style={styles.svg}>
        {/* Background circle */}
        <Circle
          stroke={COLORS.GRAY_LIGHT}
          fill="transparent"
          strokeWidth={strokeWidth}
          r={normalizedRadius}
          cx={size / 2}
          cy={size / 2}
        />
        
        {/* Progress circle */}
        <Circle
          stroke={getColor()}
          fill="transparent"
          strokeWidth={strokeWidth}
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          r={normalizedRadius}
          cx={size / 2}
          cy={size / 2}
          transform={`rotate(-90 ${size / 2} ${size / 2})`}
        />
      </Svg>
      
      <View style={styles.content}>
        <Text style={styles.bacValue}>
          {(bac * 100).toFixed(2)}%
        </Text>
        <Text style={styles.bacLabel}>BAC</Text>
        <Text style={[styles.status, { color: getColor() }]}>
          {getStatus()}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  svg: {
    position: 'absolute',
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  bacValue: {
    fontSize: FONT_SIZES.XXL + 8,
    fontWeight: 'bold',
    color: COLORS.BLACK,
  },
  bacLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.GRAY_MEDIUM,
    marginTop: -4,
  },
  status: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    marginTop: SPACING.XS,
    textAlign: 'center',
  },
});

export default BACMeter;

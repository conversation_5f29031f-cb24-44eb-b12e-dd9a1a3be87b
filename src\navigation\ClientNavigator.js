import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Import client screens
import { 
  ClientHomeScreen,
  AlcoholEntryScreen,
  BACResultScreen,
  DriverSearchScreen,
  TripStatusScreen
} from '../screens';

import { COLORS } from '../utils/constants';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

const ClientHomeStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="ClientHome" component={ClientHomeScreen} />
      <Stack.Screen name="AlcoholEntry" component={AlcoholEntryScreen} />
      <Stack.Screen name="BACResult" component={BACResultScreen} />
      <Stack.Screen name="DriverSearch" component={DriverSearchScreen} />
      <Stack.Screen name="TripStatus" component={TripStatusScreen} />
    </Stack.Navigator>
  );
};

const ClientNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Home') {
            iconName = 'home';
          } else if (route.name === 'Profile') {
            iconName = 'person';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: COLORS.PRIMARY,
        tabBarInactiveTintColor: COLORS.GRAY_MEDIUM,
        headerShown: false,
      })}
    >
      <Tab.Screen name="Home" component={ClientHomeStack} />
      <Tab.Screen name="Profile" component={ClientHomeScreen} />
    </Tab.Navigator>
  );
};

export default ClientNavigator;

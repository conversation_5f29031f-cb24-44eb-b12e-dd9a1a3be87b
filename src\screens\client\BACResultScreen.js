import React, { useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useDispatch } from 'react-redux';
import { MaterialIcons as Icon } from '@expo/vector-icons';

import { <PERSON><PERSON>, Header, Card, BACMeter } from '../../components';
import { COLORS, FONT_SIZES, SPACING, BAC_LIMIT_TUNISIA } from '../../utils/constants';
import { setBACResult } from '../../store/slices/bacSlice';
import { calculateTimeUntilSafe, getBACStatus } from '../../utils/bacCalculator';

const BACResultScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useDispatch();

  const { bacResult, drinks, hoursElapsed } = route.params || {};
  const { bac, canDrive } = bacResult || {};

  useEffect(() => {
    if (bacResult) {
      dispatch(setBACResult(bacResult));
    }
  }, [bacResult, dispatch]);

  const bacStatus = getBACStatus(bac);
  const timeUntilSafe = calculateTimeUntilSafe(bac);

  const handleFindDriver = () => {
    navigation.navigate('DriverSearch', {
      currentBAC: bac,
      urgency: bac > 0.08 ? 'high' : 'medium'
    });
  };

  const handleEmergencyCall = () => {
    Alert.alert(
      'Emergency Services',
      'Do you need immediate medical assistance?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Call Emergency', style: 'destructive', onPress: () => {
          // In a real app, this would call emergency services
          Alert.alert('Emergency Called', 'Emergency services have been contacted.');
        }}
      ]
    );
  };

  const handleAddMoreDrinks = () => {
    navigation.goBack();
  };

  const handleGoHome = () => {
    navigation.navigate('ClientHome');
  };

  const getSafetyRecommendations = () => {
    if (bac <= BAC_LIMIT_TUNISIA) {
      return [
        'You are within the legal limit to drive',
        'Stay hydrated and drive carefully',
        'Consider your comfort level before driving'
      ];
    } else if (bac <= 0.08) {
      return [
        'Do not drive - you are above the legal limit',
        'Wait for your BAC to decrease or find alternative transportation',
        'Drink water and eat food to help your body process alcohol'
      ];
    } else {
      return [
        'DANGER: Your BAC is dangerously high',
        'Do not drive under any circumstances',
        'Consider seeking medical attention if you feel unwell',
        'Find a safe ride home immediately'
      ];
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="BAC Result"
        subtitle="Your current blood alcohol level"
        showBackButton
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* BAC Meter */}
          <View style={styles.meterContainer}>
            <BACMeter bac={bac} size={220} />
          </View>

          {/* Status Card */}
          <Card style={[styles.statusCard, { borderLeftColor: bacStatus.color }]}>
            <View style={styles.statusHeader}>
              <Icon
                name={canDrive ? 'check-circle' : 'warning'}
                size={24}
                color={bacStatus.color}
              />
              <Text style={[styles.statusTitle, { color: bacStatus.color }]}>
                {bacStatus.message}
              </Text>
            </View>

            <Text style={styles.statusDescription}>
              {canDrive
                ? 'Your BAC is within Tunisia\'s legal limit of 0.03%. You may drive, but please be cautious.'
                : `Your BAC is above Tunisia's legal limit. ${timeUntilSafe > 0 ? `Wait ${timeUntilSafe.toFixed(1)} hours or find alternative transportation.` : 'Find alternative transportation.'}`
              }
            </Text>
          </Card>

          {/* Details Card */}
          <Card style={styles.detailsCard}>
            <Text style={styles.cardTitle}>Details</Text>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Current BAC:</Text>
              <Text style={styles.detailValue}>{(bac * 100).toFixed(2)}%</Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Legal Limit (Tunisia):</Text>
              <Text style={styles.detailValue}>{(BAC_LIMIT_TUNISIA * 100).toFixed(1)}%</Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Time Since First Drink:</Text>
              <Text style={styles.detailValue}>{hoursElapsed}h</Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Total Drinks:</Text>
              <Text style={styles.detailValue}>
                {drinks?.reduce((total, drink) => total + drink.quantity, 0) || 0}
              </Text>
            </View>

            {timeUntilSafe > 0 && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Time Until Safe:</Text>
                <Text style={[styles.detailValue, { color: COLORS.WARNING }]}>
                  {timeUntilSafe.toFixed(1)}h
                </Text>
              </View>
            )}
          </Card>

          {/* Safety Recommendations */}
          <Card style={styles.recommendationsCard}>
            <Text style={styles.cardTitle}>Safety Recommendations</Text>
            {getSafetyRecommendations().map((recommendation, index) => (
              <View key={index} style={styles.recommendationItem}>
                <Icon name="fiber-manual-record" size={8} color={COLORS.GRAY_MEDIUM} />
                <Text style={styles.recommendationText}>{recommendation}</Text>
              </View>
            ))}
          </Card>
        </View>
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.footer}>
        {!canDrive && (
          <Button
            title="Find a Driver"
            onPress={handleFindDriver}
            style={styles.actionButton}
            variant="primary"
          />
        )}

        {bac > 0.08 && (
          <Button
            title="Emergency Help"
            onPress={handleEmergencyCall}
            style={styles.actionButton}
            variant="danger"
          />
        )}

        <View style={styles.secondaryButtons}>
          <TouchableOpacity onPress={handleAddMoreDrinks} style={styles.secondaryButton}>
            <Text style={styles.secondaryButtonText}>Add More Drinks</Text>
          </TouchableOpacity>

          <TouchableOpacity onPress={handleGoHome} style={styles.secondaryButton}>
            <Text style={styles.secondaryButtonText}>Go Home</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.WHITE,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: SPACING.MD,
    paddingBottom: SPACING.XXL,
  },
  meterContainer: {
    alignItems: 'center',
    paddingVertical: SPACING.XL,
  },
  statusCard: {
    borderLeftWidth: 4,
    marginBottom: SPACING.MD,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  statusTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    marginLeft: SPACING.SM,
  },
  statusDescription: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.GRAY_DARK,
    lineHeight: 22,
  },
  detailsCard: {
    marginBottom: SPACING.MD,
  },
  cardTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.BLACK,
    marginBottom: SPACING.MD,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  detailLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.GRAY_DARK,
  },
  detailValue: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.BLACK,
  },
  recommendationsCard: {
    marginBottom: SPACING.MD,
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: SPACING.SM,
  },
  recommendationText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.GRAY_DARK,
    marginLeft: SPACING.SM,
    flex: 1,
    lineHeight: 20,
  },
  footer: {
    padding: SPACING.MD,
    backgroundColor: COLORS.WHITE,
    borderTopWidth: 1,
    borderTopColor: COLORS.GRAY_LIGHT,
  },
  actionButton: {
    marginBottom: SPACING.SM,
  },
  secondaryButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: SPACING.SM,
  },
  secondaryButton: {
    paddingVertical: SPACING.SM,
    paddingHorizontal: SPACING.MD,
  },
  secondaryButtonText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.PRIMARY,
    fontWeight: '500',
  },
});

export default BACResultScreen;

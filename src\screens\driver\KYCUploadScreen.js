import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';

const KYCUploadScreen = () => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>KYC Upload</Text>
        <Text style={styles.subtitle}>Upload your driver license and CIN</Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: COLORS.WHITE },
  content: { flex: 1, paddingHorizontal: SPACING.XL, paddingTop: SPACING.XL },
  title: { fontSize: FONT_SIZES.XXL, fontWeight: 'bold', color: COLORS.PRIMARY, marginBottom: SPACING.SM },
  subtitle: { fontSize: FONT_SIZES.LG, color: COLORS.GRAY_MEDIUM, marginBottom: SPACING.XXL },
});

export default KYCUploadScreen;

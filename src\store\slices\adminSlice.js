import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  users: [],
  pendingKYC: [],
  trips: [],
  statistics: {
    totalUsers: 0,
    totalDrivers: 0,
    totalTrips: 0,
    pendingKYCCount: 0,
  },
  loading: false,
  error: null,
};

const adminSlice = createSlice({
  name: 'admin',
  initialState,
  reducers: {
    setUsers: (state, action) => {
      state.users = action.payload;
    },
    setPendingKYC: (state, action) => {
      state.pendingKYC = action.payload;
    },
    setTrips: (state, action) => {
      state.trips = action.payload;
    },
    setStatistics: (state, action) => {
      state.statistics = action.payload;
    },
    approveKYC: (state, action) => {
      const kycId = action.payload;
      state.pendingKYC = state.pendingKYC.filter(kyc => kyc.id !== kycId);
    },
    rejectKYC: (state, action) => {
      const kycId = action.payload;
      state.pendingKYC = state.pendingKYC.filter(kyc => kyc.id !== kycId);
    },
    clearAdminData: (state) => {
      state.users = [];
      state.pendingKYC = [];
      state.trips = [];
      state.statistics = {
        totalUsers: 0,
        totalDrivers: 0,
        totalTrips: 0,
        pendingKYCCount: 0,
      };
      state.loading = false;
      state.error = null;
    },
  },
});

export const {
  setUsers,
  setPendingKYC,
  setTrips,
  setStatistics,
  approveKYC,
  rejectKYC,
  clearAdminData,
} = adminSlice.actions;

export default adminSlice.reducer;

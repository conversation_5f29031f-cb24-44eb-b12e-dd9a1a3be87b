{"expo": {"name": "AlcFront", "slug": "alcfront", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#1e40af"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.alcfront.app", "infoPlist": {"NSLocationWhenInUseUsageDescription": "AlcFront needs access to your location to find nearby drivers and provide accurate services.", "NSLocationAlwaysAndWhenInUseUsageDescription": "AlcFront needs access to your location to find nearby drivers and provide accurate services.", "NSCameraUsageDescription": "AlcFront needs access to your camera to upload driver license and CIN documents.", "NSPhotoLibraryUsageDescription": "AlcFront needs access to your photo library to upload driver license and CIN documents."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#1e40af"}, "package": "com.alcfront.app", "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-location", "expo-image-picker", ["expo-build-properties", {"android": {"compileSdkVersion": 34, "targetSdkVersion": 34, "buildToolsVersion": "34.0.0"}, "ios": {"deploymentTarget": "13.0"}}]], "extra": {"eas": {"projectId": "your-project-id-here"}}}}
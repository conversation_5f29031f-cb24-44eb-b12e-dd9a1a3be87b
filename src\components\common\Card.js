import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { COLORS, SPACING } from '../../utils/constants';

const Card = ({
  children,
  style,
  onPress,
  variant = 'default',
  padding = 'medium',
  shadow = true,
  ...props
}) => {
  const getCardStyle = () => {
    const baseStyle = [styles.card];
    
    if (shadow) {
      baseStyle.push(styles.shadow);
    }
    
    if (variant === 'outlined') {
      baseStyle.push(styles.outlined);
    } else if (variant === 'elevated') {
      baseStyle.push(styles.elevated);
    }
    
    if (padding === 'small') {
      baseStyle.push(styles.smallPadding);
    } else if (padding === 'medium') {
      baseStyle.push(styles.mediumPadding);
    } else if (padding === 'large') {
      baseStyle.push(styles.largePadding);
    } else if (padding === 'none') {
      baseStyle.push(styles.noPadding);
    }
    
    return baseStyle;
  };

  if (onPress) {
    return (
      <TouchableOpacity
        style={[getCardStyle(), style]}
        onPress={onPress}
        activeOpacity={0.7}
        {...props}
      >
        {children}
      </TouchableOpacity>
    );
  }

  return (
    <View style={[getCardStyle(), style]} {...props}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: COLORS.WHITE,
    borderRadius: 16,
    marginBottom: SPACING.MD,
  },
  shadow: {
    shadowColor: COLORS.BLACK,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  outlined: {
    borderWidth: 1,
    borderColor: COLORS.GRAY_LIGHT,
  },
  elevated: {
    shadowColor: COLORS.BLACK,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  
  // Padding variants
  noPadding: {
    padding: 0,
  },
  smallPadding: {
    padding: SPACING.SM,
  },
  mediumPadding: {
    padding: SPACING.MD,
  },
  largePadding: {
    padding: SPACING.LG,
  },
});

export default Card;

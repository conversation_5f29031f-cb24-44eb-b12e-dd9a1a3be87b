import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { useDispatch } from 'react-redux';

import { Button, Input, Header } from '../../components';
import { COLORS, FONT_SIZES, SPACING, GENDER } from '../../utils/constants';
import { validateEmail, validatePhoneNumber, validatePassword, validateName, validateWeight, validateGender } from '../../utils/validation';
import { registerUser } from '../../services/authService';
import { loginStart, loginSuccess, loginFailure } from '../../store/slices/authSlice';

const SignupScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    gender: '',
    weight: '',
    role: 'client', // Default role
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    const firstNameError = validateName(formData.firstName, 'First name');
    if (firstNameError) newErrors.firstName = firstNameError;

    const lastNameError = validateName(formData.lastName, 'Last name');
    if (lastNameError) newErrors.lastName = lastNameError;

    const emailError = validateEmail(formData.email);
    if (emailError) newErrors.email = emailError;

    const phoneError = validatePhoneNumber(formData.phone);
    if (phoneError) newErrors.phone = phoneError;

    const passwordError = validatePassword(formData.password);
    if (passwordError) newErrors.password = passwordError;

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    const genderError = validateGender(formData.gender);
    if (genderError) newErrors.gender = genderError;

    const weightError = validateWeight(formData.weight);
    if (weightError) newErrors.weight = weightError;

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSignup = async () => {
    if (!validateForm()) return;

    setLoading(true);
    dispatch(loginStart());

    try {
      const userData = {
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        email: formData.email.trim().toLowerCase(),
        phone: formData.phone,
        password: formData.password,
        role: formData.role,
        gender: formData.gender.toLowerCase(),
        weight: parseFloat(formData.weight),
      };

      const result = await registerUser(userData);

      if (result.success) {
        dispatch(loginSuccess(result.data));
        // Navigate to phone verification
        navigation.navigate('PhoneVerification', {
          phoneNumber: formData.phone
        });
      } else {
        dispatch(loginFailure(result.error));
        Alert.alert('Registration Failed', result.error);
      }
    } catch (error) {
      const errorMessage = 'An unexpected error occurred. Please try again.';
      dispatch(loginFailure(errorMessage));
      Alert.alert('Error', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const navigateToLogin = () => {
    navigation.navigate('Login');
  };

  const selectGender = (gender) => {
    handleInputChange('gender', gender);
  };

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="Create Account"
        subtitle="Join AlcFront today"
        showBackButton
        onBackPress={() => navigation.goBack()}
      />

      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.content}>
            <View style={styles.form}>
              <View style={styles.nameRow}>
                <Input
                  label="First Name"
                  placeholder="Enter first name"
                  value={formData.firstName}
                  onChangeText={(value) => handleInputChange('firstName', value)}
                  error={errors.firstName}
                  style={styles.nameInput}
                />
                <Input
                  label="Last Name"
                  placeholder="Enter last name"
                  value={formData.lastName}
                  onChangeText={(value) => handleInputChange('lastName', value)}
                  error={errors.lastName}
                  style={styles.nameInput}
                />
              </View>

              <Input
                label="Email Address"
                placeholder="Enter your email address"
                value={formData.email}
                onChangeText={(value) => handleInputChange('email', value)}
                error={errors.email}
                keyboardType="email-address"
                leftIcon="email"
              />

              <Input
                label="Phone Number"
                placeholder="Enter your phone number"
                value={formData.phone}
                onChangeText={(value) => handleInputChange('phone', value)}
                error={errors.phone}
                keyboardType="phone-pad"
                leftIcon="phone"
              />

              <Input
                label="Password"
                placeholder="Create a password"
                value={formData.password}
                onChangeText={(value) => handleInputChange('password', value)}
                error={errors.password}
                secureTextEntry
                leftIcon="lock"
              />

              <Input
                label="Confirm Password"
                placeholder="Confirm your password"
                value={formData.confirmPassword}
                onChangeText={(value) => handleInputChange('confirmPassword', value)}
                error={errors.confirmPassword}
                secureTextEntry
                leftIcon="lock"
              />

              <View style={styles.genderContainer}>
                <Text style={styles.genderLabel}>Gender</Text>
                <View style={styles.genderButtons}>
                  <TouchableOpacity
                    style={[
                      styles.genderButton,
                      formData.gender === GENDER.MALE && styles.genderButtonSelected
                    ]}
                    onPress={() => selectGender(GENDER.MALE)}
                  >
                    <Text style={[
                      styles.genderButtonText,
                      formData.gender === GENDER.MALE && styles.genderButtonTextSelected
                    ]}>
                      Male
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.genderButton,
                      formData.gender === GENDER.FEMALE && styles.genderButtonSelected
                    ]}
                    onPress={() => selectGender(GENDER.FEMALE)}
                  >
                    <Text style={[
                      styles.genderButtonText,
                      formData.gender === GENDER.FEMALE && styles.genderButtonTextSelected
                    ]}>
                      Female
                    </Text>
                  </TouchableOpacity>
                </View>
                {errors.gender && <Text style={styles.errorText}>{errors.gender}</Text>}
              </View>

              <Input
                label="Weight (kg)"
                placeholder="Enter your weight"
                value={formData.weight}
                onChangeText={(value) => handleInputChange('weight', value)}
                error={errors.weight}
                keyboardType="numeric"
                leftIcon="fitness-center"
              />

              <Button
                title="Create Account"
                onPress={handleSignup}
                loading={loading}
                style={styles.signupButton}
              />
            </View>

            <View style={styles.footer}>
              <Text style={styles.footerText}>Already have an account? </Text>
              <TouchableOpacity onPress={navigateToLogin}>
                <Text style={styles.loginLink}>Sign In</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.WHITE,
  },
  keyboardView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.XL,
    paddingVertical: SPACING.LG,
  },
  form: {
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nameInput: {
    flex: 1,
    marginHorizontal: SPACING.XS,
  },
  genderContainer: {
    marginBottom: SPACING.MD,
  },
  genderLabel: {
    fontSize: FONT_SIZES.SM,
    fontWeight: '600',
    color: COLORS.BLACK,
    marginBottom: SPACING.XS,
  },
  genderButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  genderButton: {
    flex: 1,
    height: 48,
    borderWidth: 1,
    borderColor: COLORS.GRAY_MEDIUM,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: SPACING.XS,
    backgroundColor: COLORS.WHITE,
  },
  genderButtonSelected: {
    borderColor: COLORS.PRIMARY,
    backgroundColor: COLORS.PRIMARY,
  },
  genderButtonText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.GRAY_MEDIUM,
    fontWeight: '500',
  },
  genderButtonTextSelected: {
    color: COLORS.WHITE,
  },
  errorText: {
    fontSize: FONT_SIZES.XS,
    color: COLORS.DANGER,
    marginTop: SPACING.XS,
    marginLeft: SPACING.XS,
  },
  signupButton: {
    marginTop: SPACING.LG,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.LG,
  },
  footerText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.GRAY_MEDIUM,
  },
  loginLink: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.PRIMARY,
    fontWeight: '600',
  },
});

export default SignupScreen;

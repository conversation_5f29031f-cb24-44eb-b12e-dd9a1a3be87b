# React Native Integration Guide (JavaScript)
## BAC Calculator & Driver Service API

This guide provides everything the React Native frontend team needs to know to integrate with the BAC Calculator backend API using **JavaScript** (no TypeScript required).

## 📱 Overview

The backend provides a complete ride-sharing platform with BAC calculation, driver discovery, and safety features specifically designed for Tunisia. The API follows RESTful principles and uses JWT authentication.

## 🔧 Setup & Configuration

### Base Configuration

```javascript
// config/api.js
export const API_CONFIG = {
  BASE_URL: __DEV__ 
    ? 'http://localhost:3000/api'  // Development
    : 'https://your-production-url.com/api', // Production
  TIMEOUT: 10000,
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  }
};

// Tunisia coordinates for default location
export const TUNISIA_COORDS = {
  TUNIS: { latitude: 36.8065, longitude: 10.1815 },
  CARTHAGE: { latitude: 36.8485, longitude: 10.1980 },
  SFAX: { latitude: 34.7406, longitude: 10.7603 },
};
```

### API Client Setup

```javascript
// services/apiClient.js
import AsyncStorage from '@react-native-async-storage/async-storage';

class ApiClient {
  constructor() {
    this.baseURL = API_CONFIG.BASE_URL;
    this.token = null;
  }

  async setAuthToken(token) {
    this.token = token;
    await AsyncStorage.setItem('authToken', token);
  }

  async getAuthToken() {
    if (!this.token) {
      this.token = await AsyncStorage.getItem('authToken');
    }
    return this.token;
  }

  async request(endpoint, options = {}) {
    const token = await this.getAuthToken();
    
    const config = {
      method: 'GET',
      headers: {
        ...API_CONFIG.HEADERS,
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    if (config.body && typeof config.body === 'object') {
      config.body = JSON.stringify(config.body);
    }

    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, config);
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'API request failed');
      }

      return await response.json();
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  // HTTP Methods
  get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;
    return this.request(url);
  }

  post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: data,
    });
  }

  put(endpoint, data) {
    return this.request(endpoint, {
      method: 'PUT',
      body: data,
    });
  }

  delete(endpoint) {
    return this.request(endpoint, {
      method: 'DELETE',
    });
  }
}

export default new ApiClient();
```

## 🔐 Authentication Flow

### User Registration & Login

```javascript
// services/authService.js
import apiClient from './apiClient';

export const authService = {
  // Client Registration
  async registerClient(userData) {
    const response = await apiClient.post('/auth/signup', {
      email: userData.email,
      password: userData.password,
      phone: userData.phone,
      firstName: userData.firstName,
      lastName: userData.lastName,
      role: 'client',
      weight: userData.weight, // Required for BAC calculation
      gender: userData.gender, // 'male' or 'female'
    });
    
    if (response.token) {
      await apiClient.setAuthToken(response.token);
    }
    
    return response;
  },

  // Driver Registration
  async registerDriver(userData) {
    const response = await apiClient.post('/auth/signup', {
      email: userData.email,
      password: userData.password,
      phone: userData.phone,
      firstName: userData.firstName,
      lastName: userData.lastName,
      role: 'driver',
    });
    
    if (response.token) {
      await apiClient.setAuthToken(response.token);
    }
    
    return response;
  },

  // Login
  async login(email, password) {
    const response = await apiClient.post('/auth/login', {
      email,
      password,
    });
    
    if (response.token) {
      await apiClient.setAuthToken(response.token);
    }
    
    return response;
  },

  // Logout
  async logout() {
    await AsyncStorage.removeItem('authToken');
    apiClient.token = null;
  },
};
```

### Driver Document Upload

```javascript
// services/driverService.js
export const driverService = {
  async completeRegistration(driverData, documents) {
    const formData = new FormData();
    
    // Add text fields
    formData.append('licenseNumber', driverData.licenseNumber);
    formData.append('cinNumber', driverData.cinNumber);
    formData.append('vehicleInfo', JSON.stringify(driverData.vehicleInfo));
    
    // Add document files
    formData.append('licenseDocument', {
      uri: documents.license.uri,
      type: documents.license.type,
      name: documents.license.name,
    });
    
    formData.append('cinDocument', {
      uri: documents.cin.uri,
      type: documents.cin.type,
      name: documents.cin.name,
    });

    return await apiClient.request('/drivers/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      body: formData,
    });
  },
};
```

## 🍺 BAC Calculation Integration

### Drink Logging & BAC Monitoring

```javascript
// services/bacService.js
export const bacService = {
  // Get drink catalog
  async getDrinkCatalog(category = null) {
    const params = category ? { category } : {};
    return await apiClient.get('/drinks/catalog', params);
  },

  // Log drink consumption
  async logDrink(userId, drinkId, quantity) {
    return await apiClient.post(`/users/${userId}/consumption`, {
      drinkId,
      quantity, // in ml
    });
  },

  // Get current BAC
  async getCurrentBAC(userId) {
    return await apiClient.get(`/users/${userId}/bac`);
  },

  // Get comprehensive safety assessment
  async getSafetyAssessment(userId, location) {
    const params = {
      latitude: location.latitude,
      longitude: location.longitude,
    };
    return await apiClient.get(`/users/${userId}/safety-assessment`, params);
  },

  // Get consumption history
  async getConsumptionHistory(userId, days = 7) {
    return await apiClient.get(`/users/${userId}/consumption-history`, { days });
  },
};
```

### BAC Status Component Example

```javascript
// components/BACStatus.js
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import { bacService } from '../services/bacService';

const BACStatus = ({ userId, location }) => {
  const [bacData, setBacData] = useState(null);
  const [safetyAssessment, setSafetyAssessment] = useState(null);

  useEffect(() => {
    fetchBACData();
  }, [userId]);

  const fetchBACData = async () => {
    try {
      const [bac, safety] = await Promise.all([
        bacService.getCurrentBAC(userId),
        bacService.getSafetyAssessment(userId, location),
      ]);
      
      setBacData(bac);
      setSafetyAssessment(safety);
      
      // Show safety alerts
      if (safety.alerts && safety.alerts.length > 0) {
        const highPriorityAlert = safety.alerts[0];
        if (highPriorityAlert.priority >= 3) {
          Alert.alert(
            highPriorityAlert.title,
            highPriorityAlert.message,
            [{ text: 'OK' }]
          );
        }
      }
    } catch (error) {
      console.error('Error fetching BAC data:', error);
    }
  };

  const getBACColor = (bac) => {
    if (bac === 0) return '#10B981'; // Green
    if (bac <= 0.03) return '#F59E0B'; // Yellow
    if (bac <= 0.05) return '#EA580C'; // Orange
    return '#DC2626'; // Red
  };

  if (!bacData) return null;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Current BAC</Text>
      <Text style={[styles.bacValue, { color: getBACColor(bacData.currentBAC) }]}>
        {(bacData.currentBAC * 100).toFixed(2)}%
      </Text>
      <Text style={styles.status}>{bacData.message}</Text>
      
      {!bacData.isLegalToDrive && (
        <Text style={styles.warning}>
          Time until legal: {bacData.timeUntilLegal} hours
        </Text>
      )}
      
      {safetyAssessment?.driverSuggestions && (
        <Text style={styles.suggestion}>
          {safetyAssessment.driverSuggestions.nearbyDrivers.length} drivers available nearby
        </Text>
      )}
    </View>
  );
};
```

## 🚗 Trip Management Integration

### Trip Service

```javascript
// services/tripService.js
export const tripService = {
  // Request a trip
  async requestTrip(tripData) {
    return await apiClient.post('/trips/request', {
      pickupLocation: {
        latitude: tripData.pickup.latitude,
        longitude: tripData.pickup.longitude,
        address: tripData.pickup.address,
      },
      destination: {
        latitude: tripData.destination.latitude,
        longitude: tripData.destination.longitude,
        address: tripData.destination.address,
      },
      paymentMethod: tripData.paymentMethod, // 'cash' or 'online'
    });
  },

  // Get fare estimate
  async getFareEstimate(pickup, destination, paymentMethod = 'cash') {
    return await apiClient.post('/trips/estimate-fare', {
      pickupLocation: pickup,
      destination: destination,
      paymentMethod,
    });
  },

  // Get active trip (for clients)
  async getActiveTrip() {
    return await apiClient.get('/trips/active');
  },

  // Cancel trip
  async cancelTrip(tripId) {
    return await apiClient.put(`/trips/${tripId}/cancel`);
  },

  // Rate trip
  async rateTrip(tripId, rating, comment = '') {
    return await apiClient.post(`/trips/${tripId}/rate`, {
      rating, // 1-5
      comment,
    });
  },

  // Get trip history
  async getTripHistory(page = 1, limit = 10) {
    return await apiClient.get('/trips/history', { page, limit });
  },
};
```

### Driver-Specific Trip Functions

```javascript
// services/driverTripService.js
export const driverTripService = {
  // Update driver location
  async updateLocation(latitude, longitude) {
    return await apiClient.put('/drivers/location', {
      latitude,
      longitude,
    });
  },

  // Toggle online/offline status
  async toggleStatus(isOnline) {
    return await apiClient.put('/drivers/status', {
      isOnline,
    });
  },

  // Get available trips
  async getAvailableTrips(radius = 10) {
    return await apiClient.get('/trips/available', { radius });
  },

  // Accept trip
  async acceptTrip(tripId) {
    return await apiClient.put(`/trips/${tripId}/accept`);
  },

  // Start trip
  async startTrip(tripId) {
    return await apiClient.put(`/trips/${tripId}/start`);
  },

  // Complete trip
  async completeTrip(tripId) {
    return await apiClient.put(`/trips/${tripId}/complete`);
  },

  // Get active trip (for drivers)
  async getActiveTrip() {
    return await apiClient.get('/trips/driver/active');
  },
};
```

## 🛡️ Safety Features Integration

### Emergency System

```javascript
// services/emergencyService.js
export const emergencyService = {
  // Request emergency assistance
  async requestEmergency(userId, location, emergencyType, message = '') {
    return await apiClient.post(`/users/${userId}/emergency-request`, {
      latitude: location.latitude,
      longitude: location.longitude,
      emergencyType, // 'medical', 'safety', 'transport'
      message,
    });
  },

  // Get safety tips
  async getSafetyTips(userId) {
    return await apiClient.get(`/users/${userId}/safety-tips`);
  },

  // Tunisia emergency contacts
  getEmergencyContacts() {
    return [
      { service: 'Police', number: '190', description: 'Emergency police services' },
      { service: 'Medical Emergency', number: '198', description: 'Medical emergency and ambulance' },
      { service: 'Fire Department', number: '198', description: 'Fire and rescue services' },
      { service: 'Traffic Police', number: '197', description: 'Traffic incidents and road safety' },
    ];
  },
};
```

### Safety Alert Component

```javascript
// components/SafetyAlert.js
import React from 'react';
import { View, Text, TouchableOpacity, Linking, StyleSheet } from 'react-native';

const SafetyAlert = ({ alert, onDriverRequest }) => {
  const getAlertColor = (type) => {
    switch (type) {
      case 'emergency': return '#DC2626';
      case 'danger': return '#EA580C';
      case 'warning': return '#F59E0B';
      default: return '#3B82F6';
    }
  };

  const handleEmergencyCall = (number) => {
    Linking.openURL(`tel:${number}`);
  };

  return (
    <View style={[styles.container, { borderLeftColor: getAlertColor(alert.type) }]}>
      <Text style={styles.title}>{alert.title}</Text>
      <Text style={styles.message}>{alert.message}</Text>
      
      {alert.type === 'emergency' && (
        <TouchableOpacity 
          style={styles.emergencyButton}
          onPress={() => handleEmergencyCall('190')}
        >
          <Text style={styles.emergencyText}>Call Emergency: 190</Text>
        </TouchableOpacity>
      )}
      
      {alert.action && alert.action.includes('driver') && (
        <TouchableOpacity 
          style={styles.driverButton}
          onPress={onDriverRequest}
        >
          <Text style={styles.driverText}>Find Driver</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};
```

## 📍 Location & Maps Integration

### Location Service

```javascript
// services/locationService.js
import Geolocation from '@react-native-community/geolocation';
import { PermissionsAndroid, Platform } from 'react-native';

export const locationService = {
  async requestLocationPermission() {
    if (Platform.OS === 'android') {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    }
    return true;
  },

  async getCurrentLocation() {
    return new Promise((resolve, reject) => {
      Geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          });
        },
        (error) => reject(error),
        { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
      );
    });
  },

  // Find nearby drivers
  async findNearbyDrivers(location, radius = 10) {
    return await apiClient.get('/trips/find-drivers', {
      latitude: location.latitude,
      longitude: location.longitude,
      radius,
    });
  },
};
```

## 💰 Pricing & Payments

### Fare Display Component

```javascript
// components/FareEstimate.js
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { tripService } from '../services/tripService';

const FareEstimate = ({ pickup, destination, paymentMethod }) => {
  const [fareData, setFareData] = useState(null);

  useEffect(() => {
    if (pickup && destination) {
      fetchFareEstimate();
    }
  }, [pickup, destination, paymentMethod]);

  const fetchFareEstimate = async () => {
    try {
      const estimate = await tripService.getFareEstimate(pickup, destination, paymentMethod);
      setFareData(estimate);
    } catch (error) {
      console.error('Error fetching fare estimate:', error);
    }
  };

  if (!fareData) return null;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Fare Estimate</Text>
      <Text style={styles.mainFare}>{fareData.fareEstimate} TND</Text>
      <Text style={styles.range}>
        Range: {fareData.fareRange.min} - {fareData.fareRange.max} TND
      </Text>
      
      <View style={styles.breakdown}>
        <Text style={styles.breakdownTitle}>Breakdown:</Text>
        <Text>Base fare: {fareData.breakdown.baseFare} TND</Text>
        <Text>Distance ({fareData.distance}km): {fareData.breakdown.distanceFare} TND</Text>
        <Text>Time ({fareData.estimatedDuration}min): {fareData.breakdown.timeFare} TND</Text>
        {fareData.breakdown.bacPenalty > 0 && (
          <Text style={styles.penalty}>
            Safety fee: {fareData.breakdown.bacPenalty} TND
          </Text>
        )}
      </View>
    </View>
  );
};
```

## 🔄 Real-time Updates

### WebSocket Integration (Future Enhancement)

```javascript
// services/realtimeService.js
// Note: Backend doesn't have WebSocket yet, but here's how to implement when added

export class RealtimeService {
  constructor() {
    this.socket = null;
    this.listeners = new Map();
  }

  connect(userId, userRole) {
    // When WebSocket is implemented on backend
    this.socket = new WebSocket(`ws://localhost:3000/ws?userId=${userId}&role=${userRole}`);
    
    this.socket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMessage(data);
    };
  }

  handleMessage(data) {
    switch (data.type) {
      case 'TRIP_UPDATE':
        this.notifyListeners('tripUpdate', data.payload);
        break;
      case 'DRIVER_LOCATION':
        this.notifyListeners('driverLocation', data.payload);
        break;
      case 'SAFETY_ALERT':
        this.notifyListeners('safetyAlert', data.payload);
        break;
    }
  }

  subscribe(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  notifyListeners(event, data) {
    const callbacks = this.listeners.get(event) || [];
    callbacks.forEach(callback => callback(data));
  }
}
```

## 📱 Key UI/UX Considerations

### BAC Status Colors
- **Green (0%)**: Safe to drive
- **Yellow (0.01-0.03%)**: Caution advised
- **Orange (0.03-0.05%)**: Approaching limit
- **Red (>0.05%)**: Above legal limit

### Safety Thresholds
- **0.03%**: Start showing driver suggestions
- **0.05%**: Legal limit - mandatory driver suggestions
- **0.08%**: High danger - urgent driver recommendations
- **0.15%**: Emergency level - show emergency contacts

### Trip Status Flow
1. **Requested** → Show nearby drivers
2. **Accepted** → Show driver details and ETA
3. **In Progress** → Show real-time tracking
4. **Completed** → Show rating screen

### Error Handling
```javascript
// utils/errorHandler.js
export const handleApiError = (error) => {
  if (error.message.includes('Network')) {
    return 'Please check your internet connection';
  }
  if (error.message.includes('401')) {
    return 'Please log in again';
  }
  if (error.message.includes('403')) {
    return 'Access denied';
  }
  return error.message || 'Something went wrong';
};
```

## 🚀 Getting Started Checklist

1. **Setup API client** with base URL and authentication
2. **Implement authentication flow** (signup, login, logout)
3. **Add location permissions** and geolocation
4. **Create BAC monitoring** with drink logging
5. **Implement trip request flow** with fare estimates
6. **Add safety features** with automatic driver suggestions
7. **Handle driver workflows** (if building driver app)
8. **Add error handling** and loading states
9. **Test with provided Postman collection**
10. **Implement offline handling** for poor connectivity

## 📞 Support

- **API Documentation**: `API_DOCUMENTATION.md`
- **Postman Collection**: Test all endpoints
- **Backend Team**: Contact for any API questions
- **Emergency Contacts**: Always include Tunisia emergency numbers (190, 198, 197)

## 📋 Data Models & JavaScript Objects

### User Models

```javascript
// models/User.js
// User object structure
const User = {
  id: '', // string
  email: '', // string
  phone: '', // string
  firstName: '', // string
  lastName: '', // string
  role: '', // 'client' | 'driver' | 'admin'
  isVerified: false, // boolean
  createdAt: '', // string (ISO date)
  updatedAt: '', // string (ISO date)
};

// Client profile structure
const ClientProfile = {
  weight: 0, // number (kg)
  gender: '', // 'male' | 'female'
  currentBAC: 0, // number
  lastDrinkTime: null, // string (ISO date) or null
  consumptionHistory: [], // array of ConsumptionRecord
};

// Driver profile structure
const DriverProfile = {
  licenseNumber: '', // string
  cinNumber: '', // string
  licenseDocument: '', // string (file path)
  cinDocument: '', // string (file path)
  vehicleInfo: {}, // VehicleInfo object
  isApproved: false, // boolean
  isOnline: false, // boolean
  currentLocation: {}, // GeoLocation object
  rating: 0, // number
  totalTrips: 0, // number
};

// Vehicle info structure
const VehicleInfo = {
  make: '', // string
  model: '', // string
  year: 0, // number
  plateNumber: '', // string
  color: '', // string
};
```

### Trip Models

```javascript
// models/Trip.js
// Trip object structure
const Trip = {
  id: '', // string
  client: '', // string (User ID)
  driver: null, // string (User ID) or null
  pickupLocation: {}, // LocationPoint object
  destination: {}, // LocationPoint object
  status: '', // 'requested' | 'accepted' | 'in_progress' | 'completed' | 'cancelled'
  fare: 0, // number
  paymentMethod: '', // 'cash' | 'online'
  paymentStatus: '', // 'pending' | 'paid'
  requestedAt: '', // string (ISO date)
  acceptedAt: null, // string (ISO date) or null
  startedAt: null, // string (ISO date) or null
  completedAt: null, // string (ISO date) or null
  cancelledAt: null, // string (ISO date) or null
  clientBAC: 0, // number
  estimatedDuration: 0, // number (minutes)
  actualDuration: null, // number (minutes) or null
  rating: null, // TripRating object or null
};

// Location point structure
const LocationPoint = {
  type: 'Point', // string
  coordinates: [0, 0], // array [longitude, latitude]
  address: '', // string (optional)
};

// Trip rating structure
const TripRating = {
  clientRating: null, // number (1-5) or null
  driverRating: null, // number (1-5) or null
  clientComment: '', // string (optional)
  driverComment: '', // string (optional)
};
```

### BAC & Safety Models

```javascript
// models/BAC.js
// BAC data structure
const BACData = {
  currentBAC: 0, // number
  isLegalToDrive: true, // boolean
  timeUntilLegal: 0, // number (hours)
  status: '', // 'sober' | 'legal' | 'illegal'
  message: '', // string
  legalLimit: 0.05, // number
  consumptionSummary: {
    totalDrinks: 0, // number
    totalAlcoholGrams: 0, // number
    hoursElapsed: 0, // number
  },
};

// Safety assessment structure
const SafetyAssessment = {
  bac: {}, // BACData object
  safetyLevel: {}, // SafetyLevel object
  alerts: [], // array of SafetyAlert
  recommendations: [], // array of SafetyRecommendation
  driverSuggestions: null, // DriverSuggestions object or null
  fareEstimate: null, // FareEstimate object or null
  emergencyContacts: [], // array of EmergencyContact
  timestamp: '', // string (ISO date)
};

// Safety level structure
const SafetyLevel = {
  level: '', // 'safe' | 'warning' | 'illegal' | 'danger' | 'emergency'
  color: '', // string (hex color)
  priority: 0, // number (0-4)
  description: '', // string
};

// Safety alert structure
const SafetyAlert = {
  type: '', // 'info' | 'warning' | 'danger' | 'emergency'
  title: '', // string
  message: '', // string
  action: '', // string
  priority: 0, // number
};
```

### Drink Models

```javascript
// models/Drink.js
// Drink object structure
const Drink = {
  id: '', // string
  name: '', // string
  category: '', // 'beer' | 'wine' | 'spirits' | 'cocktail' | 'other'
  alcoholPercentage: 0, // number
  standardServing: 0, // number (ml)
  description: '', // string
  isActive: true, // boolean
};

// Consumption record structure
const ConsumptionRecord = {
  drinkId: '', // string
  quantity: 0, // number (ml)
  timestamp: '', // string (ISO date)
};
```

### API Response Models

```javascript
// models/ApiResponses.js
// Auth response structure
const AuthResponse = {
  token: '', // string
  user: {}, // User object
  message: '', // string
};

// Trip request response structure
const TripRequestResponse = {
  message: '', // string
  trip: {}, // Trip object
  safetyAlert: null, // SafetyAlert object or null
  suggestedDrivers: null, // DriverSuggestions object or null
  distance: 0, // number
};

// Fare estimate structure
const FareEstimate = {
  fareEstimate: 0, // number
  fareRange: {
    min: 0, // number
    max: 0, // number
  },
  breakdown: {}, // FareBreakdown object
  distance: 0, // number
  estimatedDuration: 0, // number
  appliedMultipliers: [], // array of PriceMultiplier
  currency: 'TND', // string
};

// Fare breakdown structure
const FareBreakdown = {
  baseFare: 0, // number
  distanceFare: 0, // number
  timeFare: 0, // number
  timeMultiplier: 1, // number
  bacPenalty: 0, // number
  platformFee: 0, // number
  paymentFee: 0, // number
  subtotal: 0, // number
};
```

## 🎨 UI Component Examples

### BAC Display Component

```javascript
// components/BACDisplay.js
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import PropTypes from 'prop-types';

const BACDisplay = ({ bacData, showDetails = false }) => {
  const getBACColor = (bac) => {
    if (bac === 0) return '#10B981';
    if (bac <= 0.03) return '#F59E0B';
    if (bac <= 0.05) return '#EA580C';
    return '#DC2626';
  };

  const getBACStatus = (bac) => {
    if (bac === 0) return 'Sober';
    if (bac <= 0.03) return 'Caution';
    if (bac <= 0.05) return 'Near Limit';
    return 'Over Limit';
  };

  return (
    <View style={styles.container}>
      <View style={styles.bacContainer}>
        <Text style={[styles.bacValue, { color: getBACColor(bacData.currentBAC) }]}>
          {(bacData.currentBAC * 100).toFixed(2)}%
        </Text>
        <Text style={styles.bacStatus}>
          {getBACStatus(bacData.currentBAC)}
        </Text>
      </View>

      {showDetails && (
        <View style={styles.details}>
          <Text style={styles.message}>{bacData.message}</Text>
          {!bacData.isLegalToDrive && (
            <Text style={styles.timeUntilLegal}>
              Legal in: {bacData.timeUntilLegal.toFixed(1)} hours
            </Text>
          )}
        </View>
      )}
    </View>
  );
};

BACDisplay.propTypes = {
  bacData: PropTypes.shape({
    currentBAC: PropTypes.number.isRequired,
    isLegalToDrive: PropTypes.bool.isRequired,
    timeUntilLegal: PropTypes.number.isRequired,
    message: PropTypes.string.isRequired,
  }).isRequired,
  showDetails: PropTypes.bool,
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginVertical: 8,
  },
  bacContainer: {
    alignItems: 'center',
  },
  bacValue: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  bacStatus: {
    fontSize: 16,
    color: '#666',
  },
  details: {
    marginTop: 12,
  },
  message: {
    fontSize: 14,
    textAlign: 'center',
    color: '#333',
  },
  timeUntilLegal: {
    fontSize: 12,
    textAlign: 'center',
    color: '#666',
    marginTop: 4,
  },
});

export default BACDisplay;
```

### Driver Card Component

```javascript
// components/DriverCard.js
import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import PropTypes from 'prop-types';

const DriverCard = ({ driver, onSelect, estimatedArrival }) => {
  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => onSelect(driver.id)}
    >
      <View style={styles.header}>
        <Text style={styles.name}>{driver.name}</Text>
        <View style={styles.rating}>
          <Text style={styles.ratingText}>⭐ {driver.rating.toFixed(1)}</Text>
        </View>
      </View>

      <View style={styles.vehicleInfo}>
        <Text style={styles.vehicle}>
          {driver.vehicle.color} {driver.vehicle.make} {driver.vehicle.model}
        </Text>
        <Text style={styles.plate}>{driver.vehicle.plateNumber}</Text>
      </View>

      <View style={styles.footer}>
        <Text style={styles.distance}>{driver.distance.toFixed(1)} km away</Text>
        {estimatedArrival && (
          <Text style={styles.eta}>ETA: {estimatedArrival} min</Text>
        )}
      </View>
    </TouchableOpacity>
  );
};

DriverCard.propTypes = {
  driver: PropTypes.shape({
    id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    rating: PropTypes.number.isRequired,
    distance: PropTypes.number.isRequired,
    vehicle: PropTypes.shape({
      make: PropTypes.string.isRequired,
      model: PropTypes.string.isRequired,
      color: PropTypes.string.isRequired,
      plateNumber: PropTypes.string.isRequired,
    }).isRequired,
  }).isRequired,
  onSelect: PropTypes.func.isRequired,
  estimatedArrival: PropTypes.number,
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  name: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  rating: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  ratingText: {
    fontSize: 14,
    color: '#333',
  },
  vehicleInfo: {
    marginVertical: 8,
  },
  vehicle: {
    fontSize: 16,
    color: '#666',
  },
  plate: {
    fontSize: 14,
    color: '#999',
    fontFamily: 'monospace',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  distance: {
    fontSize: 14,
    color: '#666',
  },
  eta: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
});

export default DriverCard;
```

## 🔄 State Management Examples

### Redux/Context Setup

```javascript
// store/bacSlice.js (Redux Toolkit example)
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { bacService } from '../services/bacService';

// Initial state
const initialState = {
  currentBAC: null,
  safetyAssessment: null,
  loading: false,
  error: null,
};

// Async thunks
export const fetchCurrentBAC = createAsyncThunk(
  'bac/fetchCurrent',
  async (userId) => {
    return await bacService.getCurrentBAC(userId);
  }
);

export const fetchSafetyAssessment = createAsyncThunk(
  'bac/fetchSafety',
  async ({ userId, location }) => {
    return await bacService.getSafetyAssessment(userId, location);
  }
);

export const logDrinkConsumption = createAsyncThunk(
  'bac/logDrink',
  async ({ userId, drinkId, quantity }) => {
    await bacService.logDrink(userId, drinkId, quantity);
    // Fetch updated BAC after logging drink
    return await bacService.getCurrentBAC(userId);
  }
);

// Slice
const bacSlice = createSlice({
  name: 'bac',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    resetBACData: (state) => {
      state.currentBAC = null;
      state.safetyAssessment = null;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch current BAC
      .addCase(fetchCurrentBAC.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCurrentBAC.fulfilled, (state, action) => {
        state.loading = false;
        state.currentBAC = action.payload;
      })
      .addCase(fetchCurrentBAC.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch BAC';
      })
      // Fetch safety assessment
      .addCase(fetchSafetyAssessment.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSafetyAssessment.fulfilled, (state, action) => {
        state.loading = false;
        state.safetyAssessment = action.payload;
      })
      .addCase(fetchSafetyAssessment.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch safety assessment';
      })
      // Log drink consumption
      .addCase(logDrinkConsumption.fulfilled, (state, action) => {
        state.currentBAC = action.payload;
      });
  },
});

export const { clearError, resetBACData } = bacSlice.actions;
export default bacSlice.reducer;
```

### Context API Alternative

```javascript
// context/BACContext.js
import React, { createContext, useContext, useReducer, useCallback } from 'react';
import { bacService } from '../services/bacService';

// Initial state
const initialState = {
  currentBAC: null,
  safetyAssessment: null,
  loading: false,
  error: null,
};

// Action types
const BAC_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_BAC_DATA: 'SET_BAC_DATA',
  SET_SAFETY_ASSESSMENT: 'SET_SAFETY_ASSESSMENT',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR',
  RESET_DATA: 'RESET_DATA',
};

// Reducer
const bacReducer = (state, action) => {
  switch (action.type) {
    case BAC_ACTIONS.SET_LOADING:
      return { ...state, loading: action.payload, error: null };
    case BAC_ACTIONS.SET_BAC_DATA:
      return { ...state, currentBAC: action.payload, loading: false };
    case BAC_ACTIONS.SET_SAFETY_ASSESSMENT:
      return { ...state, safetyAssessment: action.payload, loading: false };
    case BAC_ACTIONS.SET_ERROR:
      return { ...state, error: action.payload, loading: false };
    case BAC_ACTIONS.CLEAR_ERROR:
      return { ...state, error: null };
    case BAC_ACTIONS.RESET_DATA:
      return initialState;
    default:
      return state;
  }
};

// Context
const BACContext = createContext();

// Provider component
export const BACProvider = ({ children }) => {
  const [state, dispatch] = useReducer(bacReducer, initialState);

  const fetchCurrentBAC = useCallback(async (userId) => {
    try {
      dispatch({ type: BAC_ACTIONS.SET_LOADING, payload: true });
      const bacData = await bacService.getCurrentBAC(userId);
      dispatch({ type: BAC_ACTIONS.SET_BAC_DATA, payload: bacData });
    } catch (error) {
      dispatch({ type: BAC_ACTIONS.SET_ERROR, payload: error.message });
    }
  }, []);

  const fetchSafetyAssessment = useCallback(async (userId, location) => {
    try {
      dispatch({ type: BAC_ACTIONS.SET_LOADING, payload: true });
      const assessment = await bacService.getSafetyAssessment(userId, location);
      dispatch({ type: BAC_ACTIONS.SET_SAFETY_ASSESSMENT, payload: assessment });
    } catch (error) {
      dispatch({ type: BAC_ACTIONS.SET_ERROR, payload: error.message });
    }
  }, []);

  const logDrink = useCallback(async (userId, drinkId, quantity) => {
    try {
      await bacService.logDrink(userId, drinkId, quantity);
      // Refresh BAC data after logging drink
      await fetchCurrentBAC(userId);
    } catch (error) {
      dispatch({ type: BAC_ACTIONS.SET_ERROR, payload: error.message });
    }
  }, [fetchCurrentBAC]);

  const clearError = useCallback(() => {
    dispatch({ type: BAC_ACTIONS.CLEAR_ERROR });
  }, []);

  const resetData = useCallback(() => {
    dispatch({ type: BAC_ACTIONS.RESET_DATA });
  }, []);

  const value = {
    ...state,
    fetchCurrentBAC,
    fetchSafetyAssessment,
    logDrink,
    clearError,
    resetData,
  };

  return <BACContext.Provider value={value}>{children}</BACContext.Provider>;
};

// Hook to use BAC context
export const useBAC = () => {
  const context = useContext(BACContext);
  if (!context) {
    throw new Error('useBAC must be used within a BACProvider');
  }
  return context;
};
```

### Using State Management in Components

```javascript
// components/BACMonitor.js
import React, { useEffect } from 'react';
import { View, Text, ActivityIndicator, Alert } from 'react-native';
import { useSelector, useDispatch } from 'react-redux'; // For Redux
// import { useBAC } from '../context/BACContext'; // For Context API
import { fetchCurrentBAC, fetchSafetyAssessment } from '../store/bacSlice';
import BACDisplay from './BACDisplay';

const BACMonitor = ({ userId, location }) => {
  const dispatch = useDispatch();

  // Redux selectors
  const { currentBAC, safetyAssessment, loading, error } = useSelector(state => state.bac);

  // For Context API, use this instead:
  // const { currentBAC, safetyAssessment, loading, error, fetchCurrentBAC, fetchSafetyAssessment } = useBAC();

  useEffect(() => {
    if (userId) {
      dispatch(fetchCurrentBAC(userId));

      if (location) {
        dispatch(fetchSafetyAssessment({ userId, location }));
      }
    }
  }, [userId, location, dispatch]);

  useEffect(() => {
    // Show safety alerts
    if (safetyAssessment?.alerts?.length > 0) {
      const highPriorityAlert = safetyAssessment.alerts.find(alert => alert.priority >= 3);
      if (highPriorityAlert) {
        Alert.alert(
          highPriorityAlert.title,
          highPriorityAlert.message,
          [{ text: 'OK' }]
        );
      }
    }
  }, [safetyAssessment]);

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text>Calculating BAC...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={{ padding: 16 }}>
        <Text style={{ color: 'red', textAlign: 'center' }}>
          Error: {error}
        </Text>
      </View>
    );
  }

  return (
    <View>
      {currentBAC && <BACDisplay bacData={currentBAC} showDetails={true} />}

      {safetyAssessment?.driverSuggestions && (
        <View style={{ marginTop: 16 }}>
          <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 8 }}>
            Nearby Drivers Available
          </Text>
          <Text style={{ color: '#666' }}>
            {safetyAssessment.driverSuggestions.nearbyDrivers.length} drivers found
          </Text>
        </View>
      )}
    </View>
  );
};

export default BACMonitor;
```

This backend is production-ready and includes all safety features needed for a responsible drinking app in Tunisia! 🇹🇳🍺🚗

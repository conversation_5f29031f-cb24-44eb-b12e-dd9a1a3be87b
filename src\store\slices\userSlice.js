import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  profile: null,
  hasSelectedRole: false,
  preferences: {},
  loading: false,
  error: null,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUserProfile: (state, action) => {
      state.profile = action.payload;
    },
    setRoleSelected: (state, action) => {
      state.hasSelectedRole = action.payload;
    },
    updateProfile: (state, action) => {
      state.profile = { ...state.profile, ...action.payload };
    },
    setPreferences: (state, action) => {
      state.preferences = { ...state.preferences, ...action.payload };
    },
    clearUserData: (state) => {
      state.profile = null;
      state.hasSelectedRole = false;
      state.preferences = {};
      state.loading = false;
      state.error = null;
    },
  },
});

export const {
  setUserProfile,
  setRoleSelected,
  updateProfile,
  setPreferences,
  clearUserData,
} = userSlice.actions;

export default userSlice.reducer;

import React from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { StatusBar } from 'expo-status-bar';

const App = () => {
  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      <Text style={styles.text}>AlcFront App</Text>
      <Text style={styles.subtext}>Loading...</Text>
      <ActivityIndicator size="large" color="#1e40af" style={styles.spinner} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1e40af',
    marginBottom: 10,
  },
  subtext: {
    fontSize: 16,
    color: '#64748b',
    marginBottom: 20,
  },
  spinner: {
    marginTop: 20,
  },
});

export default App;

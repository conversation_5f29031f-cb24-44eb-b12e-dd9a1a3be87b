import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, RefreshControl } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { <PERSON><PERSON>, Header, Card, LoadingSpinner } from '../../components';
import { COLORS, FONT_SIZES, SPACING, TRIP_STATUS } from '../../utils/constants';
import { getTripDetails, cancelTrip, rateTrip } from '../../services/tripService';
import { formatDuration, formatCurrency } from '../../utils/helpers';

const TripStatusScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useDispatch();

  const { tripId } = route.params || {};
  const { currentTrip } = useSelector(state => state.trip);

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [trip, setTrip] = useState(currentTrip);
  const [showRating, setShowRating] = useState(false);
  const [rating, setRating] = useState(0);

  useEffect(() => {
    if (tripId) {
      loadTripDetails();
    } else if (currentTrip) {
      setTrip(currentTrip);
      setLoading(false);
    }
  }, [tripId, currentTrip]);

  const loadTripDetails = async () => {
    try {
      setLoading(true);
      const result = await getTripDetails(tripId);

      if (result.success) {
        setTrip(result.data.trip);
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to load trip details.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadTripDetails();
    setRefreshing(false);
  };

  const handleCancelTrip = () => {
    Alert.alert(
      'Cancel Trip',
      'Are you sure you want to cancel this trip? You may be charged a cancellation fee.',
      [
        { text: 'Keep Trip', style: 'cancel' },
        { text: 'Cancel Trip', style: 'destructive', onPress: confirmCancelTrip }
      ]
    );
  };

  const confirmCancelTrip = async () => {
    try {
      setLoading(true);
      const result = await cancelTrip(trip.id, 'User cancelled');

      if (result.success) {
        Alert.alert(
          'Trip Cancelled',
          'Your trip has been cancelled successfully.',
          [{ text: 'OK', onPress: () => navigation.goBack() }]
        );
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to cancel trip.');
    } finally {
      setLoading(false);
    }
  };

  const handleCallDriver = () => {
    if (trip?.driver?.phone) {
      Alert.alert(
        'Call Driver',
        `Do you want to call ${trip.driver.name}?`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Call', onPress: () => {
            // In a real app, this would initiate a phone call
            Alert.alert('Calling...', `Calling ${trip.driver.name}`);
          }}
        ]
      );
    }
  };

  const handleRateTrip = async () => {
    if (rating === 0) {
      Alert.alert('Rating Required', 'Please select a rating before submitting.');
      return;
    }

    try {
      setLoading(true);
      const result = await rateTrip(trip.id, rating);

      if (result.success) {
        Alert.alert(
          'Thank You!',
          'Your rating has been submitted successfully.',
          [{ text: 'OK', onPress: () => navigation.navigate('ClientHome') }]
        );
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to submit rating.');
    } finally {
      setLoading(false);
    }
  };

  const getStatusInfo = (status) => {
    switch (status) {
      case TRIP_STATUS.REQUESTED:
        return {
          icon: 'schedule',
          color: COLORS.WARNING,
          title: 'Trip Requested',
          description: 'Waiting for driver to accept your request...'
        };
      case TRIP_STATUS.ACCEPTED:
        return {
          icon: 'check-circle',
          color: COLORS.SUCCESS,
          title: 'Trip Accepted',
          description: 'Driver is on the way to pick you up'
        };
      case TRIP_STATUS.IN_PROGRESS:
        return {
          icon: 'directions-car',
          color: COLORS.PRIMARY,
          title: 'Trip in Progress',
          description: 'You are on your way to the destination'
        };
      case TRIP_STATUS.COMPLETED:
        return {
          icon: 'flag',
          color: COLORS.SUCCESS,
          title: 'Trip Completed',
          description: 'You have arrived at your destination'
        };
      case TRIP_STATUS.CANCELLED:
        return {
          icon: 'cancel',
          color: COLORS.DANGER,
          title: 'Trip Cancelled',
          description: 'This trip has been cancelled'
        };
      default:
        return {
          icon: 'help',
          color: COLORS.GRAY_MEDIUM,
          title: 'Unknown Status',
          description: 'Trip status is unknown'
        };
    }
  };

  if (loading && !refreshing) {
    return (
      <SafeAreaView style={styles.container}>
        <Header title="Trip Status" showBackButton onBackPress={() => navigation.goBack()} />
        <LoadingSpinner />
      </SafeAreaView>
    );
  }

  if (!trip) {
    return (
      <SafeAreaView style={styles.container}>
        <Header title="Trip Status" showBackButton onBackPress={() => navigation.goBack()} />
        <View style={styles.noTripContainer}>
          <Icon name="directions-car" size={64} color={COLORS.GRAY_MEDIUM} />
          <Text style={styles.noTripTitle}>No Active Trip</Text>
          <Text style={styles.noTripText}>You don't have any active trips at the moment.</Text>
          <Button
            title="Find a Driver"
            onPress={() => navigation.navigate('DriverSearch')}
            style={styles.findDriverButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  const statusInfo = getStatusInfo(trip.status);
  const canCancel = [TRIP_STATUS.REQUESTED, TRIP_STATUS.ACCEPTED].includes(trip.status);
  const isCompleted = trip.status === TRIP_STATUS.COMPLETED;
  const showRatingSection = isCompleted && !trip.rating;

  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="Trip Status"
        showBackButton
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          {/* Status Card */}
          <Card style={[styles.statusCard, { borderLeftColor: statusInfo.color }]}>
            <View style={styles.statusHeader}>
              <Icon name={statusInfo.icon} size={32} color={statusInfo.color} />
              <View style={styles.statusInfo}>
                <Text style={[styles.statusTitle, { color: statusInfo.color }]}>
                  {statusInfo.title}
                </Text>
                <Text style={styles.statusDescription}>
                  {statusInfo.description}
                </Text>
              </View>
            </View>
          </Card>

          {/* Driver Info */}
          {trip.driver && (
            <Card style={styles.driverCard}>
              <Text style={styles.cardTitle}>Your Driver</Text>
              <View style={styles.driverInfo}>
                <View style={styles.driverAvatar}>
                  <Icon name="person" size={32} color={COLORS.WHITE} />
                </View>
                <View style={styles.driverDetails}>
                  <Text style={styles.driverName}>{trip.driver.name}</Text>
                  <Text style={styles.driverVehicle}>
                    {trip.driver.vehicle?.color} {trip.driver.vehicle?.make} {trip.driver.vehicle?.model}
                  </Text>
                  <Text style={styles.driverPlate}>
                    {trip.driver.vehicle?.licensePlate}
                  </Text>
                </View>
                <TouchableOpacity onPress={handleCallDriver} style={styles.callButton}>
                  <Icon name="phone" size={24} color={COLORS.SUCCESS} />
                </TouchableOpacity>
              </View>
            </Card>
          )}

          {/* Trip Details */}
          <Card style={styles.detailsCard}>
            <Text style={styles.cardTitle}>Trip Details</Text>

            <View style={styles.locationRow}>
              <Icon name="my-location" size={20} color={COLORS.SUCCESS} />
              <View style={styles.locationInfo}>
                <Text style={styles.locationLabel}>Pickup</Text>
                <Text style={styles.locationAddress}>
                  {trip.pickupLocation?.address || 'Current Location'}
                </Text>
              </View>
            </View>

            <View style={styles.locationRow}>
              <Icon name="location-on" size={20} color={COLORS.DANGER} />
              <View style={styles.locationInfo}>
                <Text style={styles.locationLabel}>Destination</Text>
                <Text style={styles.locationAddress}>
                  {trip.destination?.address || 'Destination'}
                </Text>
              </View>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Fare:</Text>
              <Text style={styles.detailValue}>
                {formatCurrency(trip.fare || 0)}
              </Text>
            </View>

            {trip.estimatedDuration && (
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Estimated Duration:</Text>
                <Text style={styles.detailValue}>
                  {formatDuration(trip.estimatedDuration)}
                </Text>
              </View>
            )}

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Payment Method:</Text>
              <Text style={styles.detailValue}>
                {trip.paymentMethod || 'Cash'}
              </Text>
            </View>
          </Card>

          {/* Rating Section */}
          {showRatingSection && (
            <Card style={styles.ratingCard}>
              <Text style={styles.cardTitle}>Rate Your Trip</Text>
              <Text style={styles.ratingDescription}>
                How was your experience with {trip.driver?.name}?
              </Text>

              <View style={styles.starsContainer}>
                {[1, 2, 3, 4, 5].map((star) => (
                  <TouchableOpacity
                    key={star}
                    onPress={() => setRating(star)}
                    style={styles.starButton}
                  >
                    <Icon
                      name={star <= rating ? 'star' : 'star-border'}
                      size={32}
                      color={star <= rating ? COLORS.WARNING : COLORS.GRAY_MEDIUM}
                    />
                  </TouchableOpacity>
                ))}
              </View>

              <Button
                title="Submit Rating"
                onPress={handleRateTrip}
                disabled={rating === 0}
                style={styles.ratingButton}
              />
            </Card>
          )}
        </View>
      </ScrollView>

      {/* Bottom Actions */}
      {canCancel && (
        <View style={styles.footer}>
          <Button
            title="Cancel Trip"
            onPress={handleCancelTrip}
            variant="outline"
            style={styles.cancelButton}
          />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.WHITE,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    paddingHorizontal: SPACING.MD,
    paddingBottom: SPACING.XXL,
  },
  noTripContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.XL,
  },
  noTripTitle: {
    fontSize: FONT_SIZES.XL,
    fontWeight: 'bold',
    color: COLORS.BLACK,
    marginTop: SPACING.MD,
    marginBottom: SPACING.SM,
  },
  noTripText: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.GRAY_MEDIUM,
    textAlign: 'center',
    marginBottom: SPACING.XL,
  },
  findDriverButton: {
    alignSelf: 'center',
  },
  statusCard: {
    borderLeftWidth: 4,
    marginBottom: SPACING.MD,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusInfo: {
    marginLeft: SPACING.MD,
    flex: 1,
  },
  statusTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    marginBottom: SPACING.XS,
  },
  statusDescription: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.GRAY_DARK,
  },
  driverCard: {
    marginBottom: SPACING.MD,
  },
  cardTitle: {
    fontSize: FONT_SIZES.LG,
    fontWeight: 'bold',
    color: COLORS.BLACK,
    marginBottom: SPACING.MD,
  },
  driverInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  driverAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: COLORS.PRIMARY,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: SPACING.MD,
  },
  driverDetails: {
    flex: 1,
  },
  driverName: {
    fontSize: FONT_SIZES.MD,
    fontWeight: 'bold',
    color: COLORS.BLACK,
    marginBottom: 2,
  },
  driverVehicle: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.GRAY_DARK,
    marginBottom: 2,
  },
  driverPlate: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.GRAY_MEDIUM,
  },
  callButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.GRAY_LIGHT,
    alignItems: 'center',
    justifyContent: 'center',
  },
  detailsCard: {
    marginBottom: SPACING.MD,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: SPACING.MD,
  },
  locationInfo: {
    marginLeft: SPACING.SM,
    flex: 1,
  },
  locationLabel: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.GRAY_MEDIUM,
    marginBottom: 2,
  },
  locationAddress: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.BLACK,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.SM,
  },
  detailLabel: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.GRAY_DARK,
  },
  detailValue: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.BLACK,
  },
  ratingCard: {
    marginBottom: SPACING.MD,
    alignItems: 'center',
  },
  ratingDescription: {
    fontSize: FONT_SIZES.MD,
    color: COLORS.GRAY_DARK,
    textAlign: 'center',
    marginBottom: SPACING.LG,
  },
  starsContainer: {
    flexDirection: 'row',
    marginBottom: SPACING.LG,
  },
  starButton: {
    marginHorizontal: SPACING.XS,
  },
  ratingButton: {
    alignSelf: 'stretch',
  },
  footer: {
    padding: SPACING.MD,
    backgroundColor: COLORS.WHITE,
    borderTopWidth: 1,
    borderTopColor: COLORS.GRAY_LIGHT,
  },
  cancelButton: {
    marginBottom: SPACING.SM,
  },
});

export default TripStatusScreen;

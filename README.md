# AlcFront - BAC Tracking & Driver Discovery App

A React Native Expo app that helps users track their blood alcohol concentration (BAC) and find safe rides when they can't drive legally under Tunisian law (BAC limit: 0.03%).

## 🚀 Features

### 🍺 BAC Calculation
- Real-time BAC calculation using the Widmark formula
- Tunisia-specific legal limit (0.03%)
- Time-based alcohol metabolism tracking
- Safety recommendations and warnings

### 🚗 Driver Discovery
- Location-based driver search
- Real-time driver availability
- Trip request and status tracking
- Fare estimation

### 👥 Multi-Role Support
- **Client**: Track BAC and request rides
- **Driver**: Manage availability and accept trips
- **Admin**: User management and KYC approval

## 🛠️ Tech Stack

- **React Native Expo** - Cross-platform mobile development
- **Redux Toolkit** - State management
- **React Navigation** - Navigation system
- **Expo Location** - GPS and location services
- **Expo Vector Icons** - Icon library
- **Expo Linear Gradient** - Gradient components
- **Expo Image Picker** - Document upload functionality

## 📱 Getting Started

### Prerequisites
- Node.js (18+)
- Expo CLI
- Expo Go app on your mobile device

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd alcFront
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm start
   ```

4. **Run on device**
   - Scan the QR code with Expo Go app (Android)
   - Scan the QR code with Camera app (iOS)

### Alternative Run Commands
```bash
npm run android  # Run on Android emulator
npm run ios      # Run on iOS simulator
npm run web      # Run on web browser
```

## 🏗️ Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── common/         # Basic components (Button, Input, Card, etc.)
│   └── index.js        # Component exports
├── navigation/         # Navigation configuration
│   ├── AppNavigator.js # Main navigation
│   ├── AuthNavigator.js
│   ├── ClientNavigator.js
│   ├── DriverNavigator.js
│   └── AdminNavigator.js
├── screens/            # Screen components
│   ├── auth/          # Authentication screens
│   ├── onboarding/    # Onboarding flow
│   ├── client/        # Client-specific screens
│   ├── driver/        # Driver-specific screens
│   └── admin/         # Admin-specific screens
├── services/          # API and external services
│   ├── api.js         # Axios configuration
│   ├── authService.js # Authentication API
│   ├── bacService.js  # BAC calculation API
│   ├── driverService.js
│   ├── tripService.js
│   └── locationService.js
├── store/             # Redux store configuration
│   ├── index.js       # Store setup
│   └── slices/        # Redux slices
└── utils/             # Utility functions
    ├── constants.js   # App constants
    ├── helpers.js     # Helper functions
    ├── validation.js  # Form validation
    └── bacCalculator.js # BAC calculation logic
```

## 🔧 Configuration

### Environment Variables
Copy `.env.example` to `.env` and configure:

```env
API_BASE_URL=http://localhost:3000/api
GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
```

### App Configuration
Edit `app.json` to customize:
- App name and bundle identifier
- Permissions
- Splash screen and icons
- Build settings

## 🧪 Testing

```bash
npm test        # Run tests
npm run lint    # Run ESLint
```

## 📦 Building for Production

### EAS Build (Recommended)
```bash
# Install EAS CLI
npm install -g @expo/eas-cli

# Configure EAS
eas build:configure

# Build for Android
eas build --platform android

# Build for iOS
eas build --platform ios
```

### Expo Build (Legacy)
```bash
expo build:android
expo build:ios
```

## 🔒 Permissions

The app requires the following permissions:
- **Location**: Find nearby drivers and provide location-based services
- **Camera**: Upload driver license and CIN documents
- **Photo Library**: Select documents from device storage

## 🌍 Localization

Currently supports:
- English (default)
- Arabic (Tunisia) - planned

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Contact the development team

---

**Drive Safe, Arrive Safe** 🚗✨

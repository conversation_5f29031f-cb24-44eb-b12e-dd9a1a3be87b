# BAC Calculator & Driver Service API Documentation

## Overview

This API provides a comprehensive backend service for a mobile application that calculates Blood Alcohol Content (BAC) and connects users with drivers when they're above the legal limit in Tunisia.

### Key Features

- 🍺 **BAC Calculation**: Real-time BAC calculation based on drink consumption
- 🚗 **Driver Discovery**: Geospatial search for nearby available drivers
- 🛡️ **Safety Features**: Automatic driver suggestions when BAC is high
- 📱 **Trip Management**: Complete ride-sharing workflow
- 👮 **Admin Controls**: Driver approval and platform management
- 🇹🇳 **Tunisia-Specific**: Legal limits and local market pricing

## Base URL

```
http://localhost:3000/api
```

## Authentication

The API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## User Roles

- **Client**: Can log drinks, check BAC, request trips
- **Driver**: Can accept trips, update location, manage availability
- **Admin**: Can approve drivers, manage platform, view analytics

## Quick Start

### 1. Setup and Seed Data

```bash
# Install dependencies
npm install

# Start the server
npm start

# Seed the database with sample data
node scripts/seedData.js
```

### 2. Test Accounts

After seeding, you can use these test accounts:

**Admin:**
- Email: `<EMAIL>`
- Password: `admin123`

**Client:**
- Email: `<EMAIL>`
- Password: `client123`

**Driver:**
- Email: `<EMAIL>`
- Password: `driver123`

### 3. Import Postman Collection

Import the provided Postman collection and environment:
- `postman/BAC_Calculator_API.postman_collection.json`
- `postman/BAC_Calculator_Environment.postman_environment.json`

## Core API Endpoints

### Authentication

#### Sign Up
```http
POST /auth/signup
```

**Body (Client):**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "+***********",
  "firstName": "Ahmed",
  "lastName": "Ben Ali",
  "role": "client",
  "weight": 75,
  "gender": "male"
}
```

**Body (Driver):**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "+***********",
  "firstName": "Mohamed",
  "lastName": "Trabelsi",
  "role": "driver"
}
```

#### Login
```http
POST /auth/login
```

**Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### BAC Calculation

#### Log Drink Consumption
```http
POST /users/{userId}/consumption
```

**Body:**
```json
{
  "drinkId": "drink_object_id",
  "quantity": 330
}
```

#### Get Current BAC
```http
GET /users/{userId}/bac
```

**Response:**
```json
{
  "currentBAC": 0.08,
  "isLegalToDrive": false,
  "timeUntilLegal": 2.5,
  "status": "illegal",
  "message": "Your BAC is above the legal limit. Wait 2.5 hour(s) or find a driver",
  "legalLimit": 0.05
}
```

#### Get Safety Assessment
```http
GET /users/{userId}/safety-assessment?latitude=36.8065&longitude=10.1815
```

**Response:**
```json
{
  "bac": {
    "currentBAC": 0.08,
    "isLegalToDrive": false,
    "status": "illegal"
  },
  "safetyLevel": {
    "level": "illegal",
    "color": "#DC2626",
    "priority": 2
  },
  "alerts": [
    {
      "type": "warning",
      "title": "Above Legal Limit",
      "message": "Your BAC (8.00%) is above Tunisia's legal limit (5.0%).",
      "action": "Use our driver service or wait until sober"
    }
  ],
  "driverSuggestions": {
    "nearbyDrivers": [...],
    "bestDriver": {...},
    "urgencyLevel": "high"
  }
}
```

### Driver Management

#### Complete Driver Registration
```http
POST /drivers/register
```

**Form Data:**
- `licenseNumber`: "TN123456789"
- `cinNumber`: "12345678"
- `vehicleInfo`: JSON string with vehicle details
- `licenseDocument`: File upload
- `cinDocument`: File upload

#### Update Driver Location
```http
PUT /drivers/location
```

**Body:**
```json
{
  "latitude": 36.8065,
  "longitude": 10.1815
}
```

#### Toggle Driver Status
```http
PUT /drivers/status
```

**Body:**
```json
{
  "isOnline": true
}
```

### Trip Management

#### Request Trip
```http
POST /trips/request
```

**Body:**
```json
{
  "pickupLocation": {
    "latitude": 36.8065,
    "longitude": 10.1815,
    "address": "Avenue Habib Bourguiba, Tunis"
  },
  "destination": {
    "latitude": 36.8485,
    "longitude": 10.1980,
    "address": "Carthage, Tunisia"
  },
  "paymentMethod": "cash"
}
```

**Response:**
```json
{
  "trip": {
    "id": "trip_id",
    "status": "requested",
    "fare": 12.50,
    "estimatedDuration": 25
  },
  "safetyAlert": {
    "level": "illegal",
    "message": "Your BAC is above the legal limit",
    "urgency": "high"
  },
  "suggestedDrivers": {
    "nearbyDrivers": [...],
    "bestDriver": {...}
  }
}
```

#### Get Fare Estimate
```http
POST /trips/estimate-fare
```

**Response:**
```json
{
  "fareEstimate": 12.50,
  "fareRange": {
    "min": 10.63,
    "max": 14.38
  },
  "breakdown": {
    "baseFare": 2.50,
    "distanceFare": 4.00,
    "timeFare": 3.75,
    "bacPenalty": 0.50,
    "platformFee": 0.30
  },
  "distance": 5.0,
  "estimatedDuration": 25,
  "currency": "TND"
}
```

### Safety Features

#### Emergency Request
```http
POST /users/{userId}/emergency-request
```

**Body:**
```json
{
  "latitude": 36.8065,
  "longitude": 10.1815,
  "emergencyType": "transport",
  "message": "Need safe transportation, BAC too high"
}
```

#### Get Safety Tips
```http
GET /users/{userId}/safety-tips
```

## Safety Thresholds

The system uses these BAC thresholds for safety assessments:

- **Legal Limit**: 0.05% (Tunisia law)
- **Warning Level**: 0.03% (Start showing warnings)
- **Danger Level**: 0.08% (High danger level)
- **Emergency Level**: 0.15% (Emergency intervention needed)

## Fare Calculation

### Pricing Structure (TND - Tunisian Dinars)

- **Base Fare**: 2.5 TND
- **Per Kilometer**: 0.8 TND
- **Per Minute**: 0.15 TND
- **Minimum Fare**: 3.0 TND
- **Platform Fee**: 0.3 TND
- **BAC Safety Fee**: 0.5 TND (when BAC > 0.05%)

### Time-Based Multipliers

- **Peak Hours** (7-9 AM, 5-7 PM): 1.3x
- **Night Hours** (10 PM - 6 AM): 1.5x
- **Weekend**: 1.1x
- **Long Distance** (>20km): 0.9x discount

## Error Handling

The API returns standard HTTP status codes:

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

**Error Response Format:**
```json
{
  "error": "Error message",
  "details": "Additional error details"
}
```

## Rate Limiting

- **General**: 100 requests per 15 minutes per IP
- **Authentication**: Additional rate limiting on login attempts

## Geospatial Features

The API uses MongoDB's geospatial capabilities:

- **2dsphere indexing** for location-based queries
- **Haversine formula** for distance calculations
- **Radius-based search** for driver discovery
- **Real-time location updates** for drivers

## Testing with Postman

1. Import the collection and environment files
2. Run the "Client Signup" request to create a user
3. Use the "Login" request to get an auth token
4. Test BAC calculation by logging drinks
5. Request a trip and see automatic driver suggestions
6. Test driver workflows with driver account

## Production Considerations

- Set up proper environment variables
- Configure MongoDB with replica sets
- Implement proper logging and monitoring
- Set up SSL/TLS certificates
- Configure CORS for your frontend domain
- Implement proper backup strategies
- Set up error tracking (e.g., Sentry)

## Support

For technical support or questions about the API, please refer to the code documentation or contact the development team.

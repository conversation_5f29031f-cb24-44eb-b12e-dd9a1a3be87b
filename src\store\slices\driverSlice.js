import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  isOnline: false,
  currentLocation: null,
  availableDrivers: [],
  currentRide: null,
  rideHistory: [],
  kycStatus: 'pending',
  loading: false,
  error: null,
};

const driverSlice = createSlice({
  name: 'driver',
  initialState,
  reducers: {
    setOnlineStatus: (state, action) => {
      state.isOnline = action.payload;
    },
    setCurrentLocation: (state, action) => {
      state.currentLocation = action.payload;
    },
    setAvailableDrivers: (state, action) => {
      state.availableDrivers = action.payload;
    },
    setCurrentRide: (state, action) => {
      state.currentRide = action.payload;
    },
    addToRideHistory: (state, action) => {
      state.rideHistory.unshift(action.payload);
    },
    setKYCStatus: (state, action) => {
      state.kycStatus = action.payload;
    },
    clearDriverData: (state) => {
      state.isOnline = false;
      state.currentLocation = null;
      state.availableDrivers = [];
      state.currentRide = null;
      state.rideHistory = [];
      state.loading = false;
      state.error = null;
    },
  },
});

export const {
  setOnlineStatus,
  setCurrentLocation,
  setAvailableDrivers,
  setCurrentRide,
  addToRideHistory,
  setKYCStatus,
  clearDriverData,
} = driverSlice.actions;

export default driverSlice.reducer;

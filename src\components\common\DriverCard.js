import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { MaterialIcons as Icon } from '@expo/vector-icons';
import { COLORS, FONT_SIZES, SPACING } from '../../utils/constants';
import Card from './Card';

const DriverCard = ({
  driver,
  onPress,
  onCall,
  showDistance = true,
  showRating = true,
  style,
}) => {
  const {
    id,
    name,
    rating = 4.5,
    distance = 0,
    estimatedTime = 5,
    vehicleType = 'Sedan',
    vehicleColor = 'White',
    licensePlate = 'TUN-123',
    profileImage,
    isOnline = true,
  } = driver;

  const formatDistance = (dist) => {
    if (dist < 1) {
      return `${Math.round(dist * 1000)}m`;
    }
    return `${dist.toFixed(1)}km`;
  };

  return (
    <Card style={[styles.container, style]} onPress={onPress} padding="medium">
      <View style={styles.header}>
        <View style={styles.driverInfo}>
          <View style={styles.avatarContainer}>
            {profileImage ? (
              <Image source={{ uri: profileImage }} style={styles.avatar} />
            ) : (
              <View style={styles.avatarPlaceholder}>
                <Icon name="person" size={24} color={COLORS.WHITE} />
              </View>
            )}
            {isOnline && <View style={styles.onlineIndicator} />}
          </View>
          
          <View style={styles.driverDetails}>
            <Text style={styles.driverName}>{name}</Text>
            
            {showRating && (
              <View style={styles.ratingContainer}>
                <Icon name="star" size={16} color={COLORS.WARNING} />
                <Text style={styles.rating}>{rating.toFixed(1)}</Text>
              </View>
            )}
            
            <Text style={styles.vehicleInfo}>
              {vehicleColor} {vehicleType} • {licensePlate}
            </Text>
          </View>
        </View>
        
        <TouchableOpacity onPress={onCall} style={styles.callButton}>
          <Icon name="phone" size={20} color={COLORS.SUCCESS} />
        </TouchableOpacity>
      </View>
      
      <View style={styles.footer}>
        {showDistance && (
          <View style={styles.distanceContainer}>
            <Icon name="location-on" size={16} color={COLORS.GRAY_MEDIUM} />
            <Text style={styles.distance}>{formatDistance(distance)} away</Text>
          </View>
        )}
        
        <View style={styles.timeContainer}>
          <Icon name="access-time" size={16} color={COLORS.GRAY_MEDIUM} />
          <Text style={styles.estimatedTime}>{estimatedTime} min arrival</Text>
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: SPACING.SM,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.MD,
  },
  driverInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: SPACING.MD,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  avatarPlaceholder: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: COLORS.GRAY_MEDIUM,
    alignItems: 'center',
    justifyContent: 'center',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: COLORS.SUCCESS,
    borderWidth: 2,
    borderColor: COLORS.WHITE,
  },
  driverDetails: {
    flex: 1,
  },
  driverName: {
    fontSize: FONT_SIZES.MD,
    fontWeight: '600',
    color: COLORS.BLACK,
    marginBottom: 2,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  rating: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.BLACK,
    marginLeft: 4,
  },
  vehicleInfo: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.GRAY_MEDIUM,
  },
  callButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.GRAY_LIGHT,
    alignItems: 'center',
    justifyContent: 'center',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  distanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  distance: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.GRAY_MEDIUM,
    marginLeft: 4,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  estimatedTime: {
    fontSize: FONT_SIZES.SM,
    color: COLORS.GRAY_MEDIUM,
    marginLeft: 4,
  },
});

export default DriverCard;

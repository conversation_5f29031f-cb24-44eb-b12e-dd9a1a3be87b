import api from './api';
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Authentication service for handling login, signup, and verification
 */

// Login user
export const loginUser = async (phoneNumber, password) => {
  try {
    const response = await api.post('/auth/login', {
      phoneNumber,
      password,
    });

    const { token, user } = response.data;

    // Store token and user data
    await AsyncStorage.setItem('authToken', token);
    await AsyncStorage.setItem('userRole', user.role);
    await AsyncStorage.setItem('userData', JSON.stringify(user));

    return { success: true, data: { token, user } };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.message || 'Login failed. Please try again.',
    };
  }
};

// Register new user
export const registerUser = async (userData) => {
  try {
    const response = await api.post('/auth/register', userData);

    const { token, user } = response.data;

    // Store token and user data
    await AsyncStorage.setItem('authToken', token);
    await AsyncStorage.setItem('userRole', user.role);
    await AsyncStorage.setItem('userData', JSON.stringify(user));

    return { success: true, data: { token, user } };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.message || 'Registration failed. Please try again.',
    };
  }
};

// Send OTP for phone verification
export const sendOTP = async (phoneNumber) => {
  try {
    const response = await api.post('/auth/send-otp', {
      phoneNumber,
    });

    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.message || 'Failed to send verification code.',
    };
  }
};

// Verify OTP
export const verifyOTP = async (phoneNumber, otp) => {
  try {
    const response = await api.post('/auth/verify-otp', {
      phoneNumber,
      otp,
    });

    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.message || 'Invalid verification code.',
    };
  }
};

// Logout user
export const logoutUser = async () => {
  try {
    // Call logout endpoint
    await api.post('/auth/logout');
  } catch (error) {
    console.log('Logout API error:', error);
  } finally {
    // Clear local storage regardless of API response
    await AsyncStorage.multiRemove([
      'authToken',
      'userRole',
      'userData',
    ]);
  }
};

// Get current user data
export const getCurrentUser = async () => {
  try {
    const response = await api.get('/auth/me');
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.message || 'Failed to get user data.',
    };
  }
};

// Refresh auth token
export const refreshToken = async () => {
  try {
    const response = await api.post('/auth/refresh');
    const { token } = response.data;

    await AsyncStorage.setItem('authToken', token);
    return { success: true, data: { token } };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.message || 'Failed to refresh token.',
    };
  }
};

// Reset password
export const resetPassword = async (phoneNumber) => {
  try {
    const response = await api.post('/auth/reset-password', {
      phoneNumber,
    });

    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.message || 'Failed to reset password.',
    };
  }
};

// Change password
export const changePassword = async (currentPassword, newPassword) => {
  try {
    const response = await api.post('/auth/change-password', {
      currentPassword,
      newPassword,
    });

    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.message || 'Failed to change password.',
    };
  }
};

// Check if user is authenticated
export const isAuthenticated = async () => {
  try {
    const token = await AsyncStorage.getItem('authToken');
    return !!token;
  } catch (error) {
    return false;
  }
};

// Get stored user data
export const getStoredUserData = async () => {
  try {
    const userData = await AsyncStorage.getItem('userData');
    const userRole = await AsyncStorage.getItem('userRole');
    
    return {
      user: userData ? JSON.parse(userData) : null,
      role: userRole,
    };
  } catch (error) {
    return { user: null, role: null };
  }
};

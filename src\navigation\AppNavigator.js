import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { useSelector } from 'react-redux';

// Import navigators
import AuthNavigator from './AuthNavigator';
import ClientNavigator from './ClientNavigator';
import DriverNavigator from './DriverNavigator';
import AdminNavigator from './AdminNavigator';

// Import screens
import { SplashScreen, RoleSelectionScreen } from '../screens';

const Stack = createStackNavigator();

const AppNavigator = () => {
  const { isAuthenticated, user } = useSelector((state) => state.auth);
  const { hasSelectedRole } = useSelector((state) => state.user);

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {!isAuthenticated ? (
          // Auth flow
          <>
            <Stack.Screen name="Splash" component={SplashScreen} />
            <Stack.Screen name="Auth" component={AuthNavigator} />
          </>
        ) : !hasSelectedRole ? (
          // Role selection
          <Stack.Screen name="RoleSelection" component={RoleSelectionScreen} />
        ) : (
          // Main app based on user role
          <>
            {user?.role === 'client' && (
              <Stack.Screen name="Client" component={ClientNavigator} />
            )}
            {user?.role === 'driver' && (
              <Stack.Screen name="Driver" component={DriverNavigator} />
            )}
            {user?.role === 'admin' && (
              <Stack.Screen name="Admin" component={AdminNavigator} />
            )}
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;

import api from './api';

/**
 * Trip service for trip requests, fare estimates, and trip management
 */

// Request a trip
export const requestTrip = async (tripData) => {
  try {
    const response = await api.post('/trips/request', tripData);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to request trip.',
    };
  }
};

// Get fare estimate
export const getFareEstimate = async (pickupLocation, destination) => {
  try {
    const response = await api.post('/trips/estimate-fare', {
      pickupLocation,
      destination,
    });
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get fare estimate.',
    };
  }
};

// Get trip details
export const getTripDetails = async (tripId) => {
  try {
    const response = await api.get(`/trips/${tripId}`);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get trip details.',
    };
  }
};

// Update trip status
export const updateTripStatus = async (tripId, status, additionalData = {}) => {
  try {
    const response = await api.put(`/trips/${tripId}/status`, {
      status,
      ...additionalData,
    });
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to update trip status.',
    };
  }
};

// Cancel trip
export const cancelTrip = async (tripId, reason) => {
  try {
    const response = await api.put(`/trips/${tripId}/cancel`, {
      reason,
    });
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to cancel trip.',
    };
  }
};

// Rate trip
export const rateTrip = async (tripId, rating, comment = '') => {
  try {
    const response = await api.post(`/trips/${tripId}/rate`, {
      rating,
      comment,
    });
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to rate trip.',
    };
  }
};

// Get user trip history
export const getUserTripHistory = async (userId, limit = 20) => {
  try {
    const response = await api.get(`/users/${userId}/trips`, {
      params: { limit }
    });
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get trip history.',
    };
  }
};

// Get active trip
export const getActiveTrip = async (userId) => {
  try {
    const response = await api.get(`/users/${userId}/active-trip`);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get active trip.',
    };
  }
};

// Accept trip (driver)
export const acceptTrip = async (tripId) => {
  try {
    const response = await api.post(`/trips/${tripId}/accept`);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to accept trip.',
    };
  }
};

// Reject trip (driver)
export const rejectTrip = async (tripId, reason = '') => {
  try {
    const response = await api.post(`/trips/${tripId}/reject`, {
      reason,
    });
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to reject trip.',
    };
  }
};

// Start trip (driver)
export const startTrip = async (tripId) => {
  try {
    const response = await api.post(`/trips/${tripId}/start`);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to start trip.',
    };
  }
};

// Complete trip (driver)
export const completeTrip = async (tripId, completionData = {}) => {
  try {
    const response = await api.post(`/trips/${tripId}/complete`, completionData);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to complete trip.',
    };
  }
};

import api from './api';

/**
 * Driver service for driver registration, location updates, and trip management
 */

// Complete driver registration with documents
export const completeDriverRegistration = async (driverData) => {
  try {
    const formData = new FormData();
    
    // Add text fields
    formData.append('licenseNumber', driverData.licenseNumber);
    formData.append('cinNumber', driverData.cinNumber);
    formData.append('vehicleInfo', JSON.stringify(driverData.vehicleInfo));
    
    // Add file uploads
    if (driverData.licenseDocument) {
      formData.append('licenseDocument', {
        uri: driverData.licenseDocument.uri,
        type: driverData.licenseDocument.mimeType || 'image/jpeg',
        name: driverData.licenseDocument.fileName || 'license.jpg',
      });
    }

    if (driverData.cinDocument) {
      formData.append('cinDocument', {
        uri: driverData.cinDocument.uri,
        type: driverData.cinDocument.mimeType || 'image/jpeg',
        name: driverData.cinDocument.fileName || 'cin.jpg',
      });
    }

    const response = await api.post('/drivers/register', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to complete driver registration.',
    };
  }
};

// Update driver location
export const updateDriverLocation = async (latitude, longitude) => {
  try {
    const response = await api.put('/drivers/location', {
      latitude,
      longitude,
    });
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to update location.',
    };
  }
};

// Toggle driver online/offline status
export const toggleDriverStatus = async (isOnline) => {
  try {
    const response = await api.put('/drivers/status', {
      isOnline,
    });
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to update driver status.',
    };
  }
};

// Get nearby drivers
export const getNearbyDrivers = async (latitude, longitude, radius = 5000) => {
  try {
    const response = await api.get('/drivers/nearby', {
      params: { latitude, longitude, radius }
    });
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get nearby drivers.',
    };
  }
};

// Get driver profile
export const getDriverProfile = async (driverId) => {
  try {
    const response = await api.get(`/drivers/${driverId}`);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get driver profile.',
    };
  }
};

// Update driver profile
export const updateDriverProfile = async (driverId, profileData) => {
  try {
    const response = await api.put(`/drivers/${driverId}`, profileData);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to update driver profile.',
    };
  }
};

// Get driver earnings
export const getDriverEarnings = async (driverId, period = 'week') => {
  try {
    const response = await api.get(`/drivers/${driverId}/earnings`, {
      params: { period }
    });
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get driver earnings.',
    };
  }
};

// Get driver trip history
export const getDriverTripHistory = async (driverId, limit = 20) => {
  try {
    const response = await api.get(`/drivers/${driverId}/trips`, {
      params: { limit }
    });
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.error || 'Failed to get trip history.',
    };
  }
};

import { WIDMARK_CONSTANTS, BAC_LIMIT_TUNISIA, ALCOHOL_TYPES } from './constants';

/**
 * Calculate Blood Alcohol Concentration using the Widmark formula
 * BAC = (A × 5.14) / (W × r) - (0.015 × H)
 * 
 * Where:
 * A = liquid ounces of alcohol consumed
 * W = body weight in pounds
 * r = gender constant (0.73 for men, 0.66 for women)
 * H = hours since drinking began
 * 
 * @param {Array} drinks - Array of drink objects with type and quantity
 * @param {number} weight - Body weight in kg
 * @param {string} gender - 'male' or 'female'
 * @param {number} hoursElapsed - Hours since drinking began
 * @returns {Object} - { bac, canDrive, drinks }
 */
export const calculateBAC = (drinks, weight, gender, hoursElapsed = 0) => {
  if (!drinks || drinks.length === 0) {
    return { bac: 0, canDrive: true, drinks: [] };
  }

  // Convert weight from kg to pounds
  const weightInPounds = weight * 2.20462;
  
  // Get gender constant
  const genderConstant = gender === 'male' ? WIDMARK_CONSTANTS.MALE : WIDMARK_CONSTANTS.FEMALE;
  
  // Calculate total alcohol consumed in grams
  let totalAlcoholGrams = 0;
  
  drinks.forEach(drink => {
    const drinkType = ALCOHOL_TYPES[drink.type.toUpperCase()];
    if (drinkType) {
      // Standard drink size: 12oz beer, 5oz wine, 1.5oz liquor, 8oz cocktail
      let drinkSizeOz;
      switch (drink.type.toLowerCase()) {
        case 'beer':
          drinkSizeOz = 12;
          break;
        case 'wine':
          drinkSizeOz = 5;
          break;
        case 'liquor':
          drinkSizeOz = 1.5;
          break;
        case 'cocktail':
          drinkSizeOz = 8;
          break;
        default:
          drinkSizeOz = 12;
      }
      
      // Calculate alcohol content in grams
      // 1 oz = 29.5735 ml, alcohol density = 0.789 g/ml
      const alcoholVolumeOz = drinkSizeOz * drinkType.alcoholContent * drink.quantity;
      const alcoholGrams = alcoholVolumeOz * 29.5735 * 0.789;
      totalAlcoholGrams += alcoholGrams;
    }
  });
  
  // Widmark formula calculation
  // Convert grams to liquid ounces (1 gram alcohol ≈ 0.0338 fl oz)
  const alcoholOunces = totalAlcoholGrams * 0.0338;
  
  // Calculate BAC
  let bac = (alcoholOunces * 5.14) / (weightInPounds * genderConstant);
  
  // Subtract alcohol metabolized over time (0.015% per hour)
  bac -= (0.015 * hoursElapsed);
  
  // BAC cannot be negative
  bac = Math.max(0, bac);
  
  // Determine if safe to drive
  const canDrive = bac <= BAC_LIMIT_TUNISIA;
  
  return {
    bac: parseFloat(bac.toFixed(4)),
    canDrive,
    drinks: drinks.map(drink => ({
      ...drink,
      alcoholContent: ALCOHOL_TYPES[drink.type.toUpperCase()]?.alcoholContent || 0
    }))
  };
};

/**
 * Calculate time until BAC drops to legal limit
 * @param {number} currentBAC - Current BAC level
 * @returns {number} - Hours until safe to drive
 */
export const calculateTimeUntilSafe = (currentBAC) => {
  if (currentBAC <= BAC_LIMIT_TUNISIA) {
    return 0;
  }
  
  const bacToReduce = currentBAC - BAC_LIMIT_TUNISIA;
  const hoursNeeded = bacToReduce / 0.015; // 0.015% per hour metabolism rate
  
  return Math.ceil(hoursNeeded * 10) / 10; // Round up to nearest 0.1 hour
};

/**
 * Get drink recommendations based on weight and gender to stay under limit
 * @param {number} weight - Body weight in kg
 * @param {string} gender - 'male' or 'female'
 * @param {string} drinkType - Type of drink
 * @returns {number} - Maximum number of drinks to stay under limit
 */
export const getMaxDrinksForLimit = (weight, gender, drinkType) => {
  const weightInPounds = weight * 2.20462;
  const genderConstant = gender === 'male' ? WIDMARK_CONSTANTS.MALE : WIDMARK_CONSTANTS.FEMALE;
  
  // Calculate BAC for one drink
  const testDrinks = [{ type: drinkType, quantity: 1 }];
  const { bac: bacPerDrink } = calculateBAC(testDrinks, weight, gender, 0);
  
  if (bacPerDrink === 0) return 0;
  
  // Calculate maximum drinks to stay under limit
  const maxDrinks = Math.floor(BAC_LIMIT_TUNISIA / bacPerDrink);
  
  return Math.max(0, maxDrinks);
};

/**
 * Format BAC for display
 * @param {number} bac - BAC value
 * @returns {string} - Formatted BAC string
 */
export const formatBAC = (bac) => {
  return `${(bac * 100).toFixed(2)}%`;
};

/**
 * Get BAC status message
 * @param {number} bac - BAC value
 * @returns {Object} - { message, color, canDrive }
 */
export const getBACStatus = (bac) => {
  if (bac <= BAC_LIMIT_TUNISIA) {
    return {
      message: 'Safe to Drive',
      color: '#10b981', // green
      canDrive: true
    };
  } else if (bac <= 0.05) {
    return {
      message: 'Caution - Do Not Drive',
      color: '#f59e0b', // yellow
      canDrive: false
    };
  } else {
    return {
      message: 'Dangerous - Do Not Drive',
      color: '#ef4444', // red
      canDrive: false
    };
  }
};
